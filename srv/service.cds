using demand.supply.reconciliation from '../db/schema';

// Main Reconciliation Service
service ReconciliationService @(path: '/reconciliation') {
  
  // Master Data - Read Only for most users
  @readonly entity Plants as projection on reconciliation.Plants;
  @readonly entity Products as projection on reconciliation.Products;
  @readonly entity Vendors as projection on reconciliation.Vendors;
  
  // Demand and Supply Data - Full CRUD
  entity Demand as projection on reconciliation.Demand;
  entity Supply as projection on reconciliation.Supply;
  entity Stock as projection on reconciliation.Stock;
  
  // Reconciliation Results - Read Only (generated by system)
  @readonly entity ReconciliationResults as projection on reconciliation.ReconciliationResults;
  @readonly entity GenAIRecommendations as projection on reconciliation.GenAIRecommendations;
  
  // Action Entities - Limited Write Access
  entity StockReallocations as projection on reconciliation.StockReallocations;
  entity ProcurementRequests as projection on reconciliation.ProcurementRequests;
  entity ProductionOrders as projection on reconciliation.ProductionOrders;
  
  // Audit Logs - Read Only
  @readonly entity ReconciliationLogs as projection on reconciliation.ReconciliationLogs;
  @readonly entity AIInteractionLogs as projection on reconciliation.AIInteractionLogs;
  
  // Custom Actions
  action runReconciliation(
    plantCode: String,
    productCode: String,
    reconciliationDate: String,
    urgency: String
  ) returns {
    success: Boolean;
    message: String;
    reconciliationId: String;
    recommendationsCount: Integer;
    aiRecommendations: array of {
      type: String;
      description: String;
      priority: String;
      confidence: Double;
      quantity: Integer;
      estimatedCost: Integer;
      estimatedTime: Integer;
      reasoning: String;
      risks: String;
      aiModel: String;
    };
    aiServiceUsed: String;
  };
  
  action approveRecommendation(
    recommendationId: String,
    approverComments: String
  ) returns {
    success: Boolean;
    message: String;
    actionTriggered: Boolean;
  };
  
  action rejectRecommendation(
    recommendationId: String,
    rejectionReason: String
  ) returns {
    success: Boolean;
    message: String;
  };
  
  action executeAction(
    actionType: String,
    actionId: String
  ) returns {
    success: Boolean;
    message: String;
    executionDetails: String;
  };
  
  action bulkDataUpload(
    dataType: String, // 'demand', 'supply', 'stock'
    data: String // JSON string of data array
  ) returns {
    success: Boolean;
    message: String;
    recordsProcessed: Integer;
    errors: array of String;
  };
  
  // Functions for Analytics
  function getDashboardKPIs(
    plantCode: String,
    dateFrom: Date,
    dateTo: Date
  ) returns {
    totalVariance: Decimal;
    shortageCount: Integer;
    surplusCount: Integer;
    pendingApprovals: Integer;
    autoTriggeredActions: Integer;
    avgConfidenceScore: Decimal;
  };
  
  function getReconciliationSummary(
    reconciliationDate: Date,
    plantCode: String
  ) returns array of {
    productCode: String;
    productName: String;
    demandQty: Decimal;
    supplyQty: Decimal;
    stockQty: Decimal;
    variance: Decimal;
    status: String;
    recommendationsCount: Integer;
  };
  
  function getActionStatus(
    actionType: String,
    dateFrom: Date,
    dateTo: Date
  ) returns array of {
    actionId: String;
    actionType: String;
    status: String;
    createdAt: DateTime;
    executedAt: DateTime;
    plant: String;
    product: String;
    quantity: Decimal;
  };
}

// Admin Service for Master Data Management
service AdminService @(path: '/admin') {
  
  // Full CRUD for Master Data
  entity Plants as projection on reconciliation.Plants;
  entity Products as projection on reconciliation.Products;
  entity Vendors as projection on reconciliation.Vendors;
  
  // System Configuration
  action resetReconciliationData(
    confirmationCode: String
  ) returns {
    success: Boolean;
    message: String;
    recordsDeleted: Integer;
  };
  
  action configureAISettings(
    aiEndpoint: String,
    confidenceThreshold: Decimal,
    autoTriggerEnabled: Boolean
  ) returns {
    success: Boolean;
    message: String;
  };
  
  // Bulk Operations
  action bulkMasterDataUpload(
    entityType: String, // 'plants', 'products', 'vendors'
    data: String // JSON string of data array
  ) returns {
    success: Boolean;
    message: String;
    recordsProcessed: Integer;
    errors: array of String;
  };
}

// Analytics Service for Reporting
service AnalyticsService @(path: '/analytics') {
  
  // Read-only views for analytics
  @readonly entity ReconciliationResults as projection on reconciliation.ReconciliationResults;
  @readonly entity GenAIRecommendations as projection on reconciliation.GenAIRecommendations;
  @readonly entity StockReallocations as projection on reconciliation.StockReallocations;
  @readonly entity ProcurementRequests as projection on reconciliation.ProcurementRequests;
  @readonly entity ProductionOrders as projection on reconciliation.ProductionOrders;
  
  // Analytics Functions
  function getVarianceTrends(
    plantCode: String,
    productCode: String,
    dateFrom: Date,
    dateTo: Date
  ) returns array of {
    date: Date;
    variance: Decimal;
    variancePercent: Decimal;
    status: String;
  };
  
  function getAIPerformanceMetrics(
    dateFrom: Date,
    dateTo: Date
  ) returns {
    totalRecommendations: Integer;
    approvedRecommendations: Integer;
    rejectedRecommendations: Integer;
    avgConfidenceScore: Decimal;
    avgProcessingTime: Integer;
    successfulExecutions: Integer;
  };
  
  function getPlantPerformance(
    dateFrom: Date,
    dateTo: Date
  ) returns array of {
    plantCode: String;
    plantName: String;
    totalVariance: Decimal;
    shortageEvents: Integer;
    surplusEvents: Integer;
    actionsExecuted: Integer;
    avgResolutionTime: Integer;
  };
}

// External Integration Service
service IntegrationService @(path: '/integration') {
  
  // External system endpoints
  action syncDemandData(
    source: String,
    dateFrom: Date,
    dateTo: Date
  ) returns {
    success: Boolean;
    message: String;
    recordsSynced: Integer;
  };
  
  action syncSupplyData(
    source: String,
    dateFrom: Date,
    dateTo: Date
  ) returns {
    success: Boolean;
    message: String;
    recordsSynced: Integer;
  };
  
  action syncStockData(
    source: String
  ) returns {
    success: Boolean;
    message: String;
    recordsSynced: Integer;
  };
  
  action notifyExternalSystem(
    system: String,
    actionType: String,
    actionId: String,
    payload: String
  ) returns {
    success: Boolean;
    message: String;
    externalRef: String;
  };
}

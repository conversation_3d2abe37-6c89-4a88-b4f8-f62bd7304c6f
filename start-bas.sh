#!/bin/bash

# 🚀 SAP BAS Startup Script for Demand-Supply Reconciliation System
# This script sets up and starts the complete application in SAP BAS

echo "🚀 Starting Demand-Supply Reconciliation System in SAP BAS..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check prerequisites
print_info "Checking prerequisites..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

print_status "Node.js and npm are available"

# Step 2: Install dependencies
print_info "Installing dependencies..."

if [ ! -d "node_modules" ]; then
    print_info "Installing npm dependencies..."
    npm install
    if [ $? -eq 0 ]; then
        print_status "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
else
    print_status "Dependencies already installed"
fi

# Step 3: Check if CDS is available
print_info "Checking CDS installation..."

if ! command -v cds &> /dev/null; then
    print_warning "CDS CLI not found globally, installing..."
    npm install -g @sap/cds-dk
fi

print_status "CDS CLI is available"

# Step 4: Deploy database
print_info "Setting up database..."

if [ ! -f "db.sqlite" ]; then
    print_info "Deploying database with sample data..."
    cds deploy --to sqlite
    if [ $? -eq 0 ]; then
        print_status "Database deployed successfully"
    else
        print_error "Failed to deploy database"
        exit 1
    fi
else
    print_status "Database already exists"
fi

# Step 5: Check if Python is available for AI service
print_info "Checking Python for AI service..."

if command -v python3 &> /dev/null; then
    print_status "Python3 is available"
    
    # Check if AI service dependencies are installed
    if [ -f "flask-api/requirements.txt" ]; then
        print_info "Installing Python dependencies for AI service..."
        cd flask-api
        pip3 install -r requirements.txt --quiet
        cd ..
        print_status "AI service dependencies installed"
    fi
else
    print_warning "Python3 not found - AI service will not be available"
fi

# Step 6: Start the application
print_info "Starting the application..."

echo ""
echo "🎯 Application will start on port 4004"
echo "📱 BAS will automatically detect the port and show a popup"
echo "🌐 Click 'Expose and Open' when prompted"
echo "📂 Navigate to: /reconciliation-dashboard/webapp/index.html"
echo ""

print_status "Starting CAP service..."

# Start the CDS service
cds watch --port 4004

# If we reach here, the service has stopped
print_info "Application stopped"

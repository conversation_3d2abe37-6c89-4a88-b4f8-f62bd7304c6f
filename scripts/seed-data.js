const cds = require('@sap/cds');

async function seedData() {
    try {
        console.log('Starting data seeding...');
        
        const db = await cds.connect.to('db');
        
        // Clear existing data (optional - be careful in production)
        console.log('Clearing existing data...');
        await db.run(DELETE.from('demand.supply.reconciliation.ReconciliationLogs'));
        await db.run(DELETE.from('demand.supply.reconciliation.AIInteractionLogs'));
        await db.run(DELETE.from('demand.supply.reconciliation.StockReallocations'));
        await db.run(DELETE.from('demand.supply.reconciliation.ProcurementRequests'));
        await db.run(DELETE.from('demand.supply.reconciliation.ProductionOrders'));
        await db.run(DELETE.from('demand.supply.reconciliation.GenAIRecommendations'));
        await db.run(DELETE.from('demand.supply.reconciliation.ReconciliationResults'));
        await db.run(DELETE.from('demand.supply.reconciliation.Stock'));
        await db.run(DELETE.from('demand.supply.reconciliation.Supply'));
        await db.run(DELETE.from('demand.supply.reconciliation.Demand'));
        await db.run(DELETE.from('demand.supply.reconciliation.Vendors'));
        await db.run(DELETE.from('demand.supply.reconciliation.Products'));
        await db.run(DELETE.from('demand.supply.reconciliation.Plants'));
        
        // Seed Plants
        console.log('Seeding plants...');
        const plants = [
            {
                ID: cds.utils.uuid(),
                plantCode: 'P001',
                plantName: 'Manufacturing Plant North',
                location: 'Detroit, MI',
                capacity: 10000,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                plantCode: 'P002',
                plantName: 'Manufacturing Plant South',
                location: 'Austin, TX',
                capacity: 8000,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                plantCode: 'P003',
                plantName: 'Manufacturing Plant West',
                location: 'Los Angeles, CA',
                capacity: 12000,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                plantCode: 'P004',
                plantName: 'Manufacturing Plant East',
                location: 'New York, NY',
                capacity: 9000,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            }
        ];
        
        await db.run(INSERT.into('demand.supply.reconciliation.Plants').entries(plants));
        
        // Seed Products
        console.log('Seeding products...');
        const products = [
            {
                ID: cds.utils.uuid(),
                productCode: 'PROD001',
                productName: 'Engine Component A',
                category: 'Engine Parts',
                unitOfMeasure: 'EA',
                standardCost: 150.00,
                currency_code: 'USD',
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                productCode: 'PROD002',
                productName: 'Transmission Unit B',
                category: 'Transmission',
                unitOfMeasure: 'EA',
                standardCost: 800.00,
                currency_code: 'USD',
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                productCode: 'PROD003',
                productName: 'Brake System C',
                category: 'Safety Systems',
                unitOfMeasure: 'EA',
                standardCost: 250.00,
                currency_code: 'USD',
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                productCode: 'PROD004',
                productName: 'Electronic Control Unit',
                category: 'Electronics',
                unitOfMeasure: 'EA',
                standardCost: 450.00,
                currency_code: 'USD',
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                productCode: 'PROD005',
                productName: 'Suspension Component',
                category: 'Chassis',
                unitOfMeasure: 'EA',
                standardCost: 320.00,
                currency_code: 'USD',
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            }
        ];
        
        await db.run(INSERT.into('demand.supply.reconciliation.Products').entries(products));
        
        // Seed Vendors
        console.log('Seeding vendors...');
        const vendors = [
            {
                ID: cds.utils.uuid(),
                vendorCode: 'V001',
                vendorName: 'Premium Auto Parts Inc.',
                contactInfo: '<EMAIL>, +1-555-0101',
                leadTime: 7,
                reliability: 0.95,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                vendorCode: 'V002',
                vendorName: 'Global Manufacturing Solutions',
                contactInfo: '<EMAIL>, +1-555-0102',
                leadTime: 10,
                reliability: 0.88,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            },
            {
                ID: cds.utils.uuid(),
                vendorCode: 'V003',
                vendorName: 'Reliable Components Ltd.',
                contactInfo: '<EMAIL>, +1-555-0103',
                leadTime: 5,
                reliability: 0.92,
                isActive: true,
                createdAt: new Date(),
                createdBy: 'SYSTEM'
            }
        ];
        
        await db.run(INSERT.into('demand.supply.reconciliation.Vendors').entries(vendors));
        
        // Get IDs for foreign key relationships
        const plantRecords = await db.run(SELECT.from('demand.supply.reconciliation.Plants'));
        const productRecords = await db.run(SELECT.from('demand.supply.reconciliation.Products'));
        const vendorRecords = await db.run(SELECT.from('demand.supply.reconciliation.Vendors'));
        
        // Seed Demand Data
        console.log('Seeding demand data...');
        const demandData = [];
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        for (let i = 0; i < plantRecords.length; i++) {
            for (let j = 0; j < productRecords.length; j++) {
                demandData.push({
                    ID: cds.utils.uuid(),
                    demandDate: today.toISOString().split('T')[0],
                    plant_ID: plantRecords[i].ID,
                    product_ID: productRecords[j].ID,
                    quantity: Math.floor(Math.random() * 500) + 100, // Random quantity between 100-600
                    priority: ['HIGH', 'MEDIUM', 'LOW'][Math.floor(Math.random() * 3)],
                    customerOrder: `CO${String(i * productRecords.length + j + 1).padStart(6, '0')}`,
                    dueDate: tomorrow.toISOString().split('T')[0],
                    status: 'OPEN',
                    createdAt: new Date(),
                    createdBy: 'SYSTEM'
                });
            }
        }
        
        await db.run(INSERT.into('demand.supply.reconciliation.Demand').entries(demandData));
        
        // Seed Supply Data
        console.log('Seeding supply data...');
        const supplyData = [];
        
        for (let i = 0; i < plantRecords.length; i++) {
            for (let j = 0; j < productRecords.length; j++) {
                supplyData.push({
                    ID: cds.utils.uuid(),
                    supplyDate: today.toISOString().split('T')[0],
                    plant_ID: plantRecords[i].ID,
                    product_ID: productRecords[j].ID,
                    quantity: Math.floor(Math.random() * 400) + 50, // Random quantity between 50-450
                    sourceType: ['PRODUCTION', 'PROCUREMENT', 'TRANSFER'][Math.floor(Math.random() * 3)],
                    sourceRef: `SR${String(i * productRecords.length + j + 1).padStart(6, '0')}`,
                    availableDate: today.toISOString().split('T')[0],
                    status: 'PLANNED',
                    createdAt: new Date(),
                    createdBy: 'SYSTEM'
                });
            }
        }
        
        await db.run(INSERT.into('demand.supply.reconciliation.Supply').entries(supplyData));
        
        // Seed Stock Data
        console.log('Seeding stock data...');
        const stockData = [];
        
        for (let i = 0; i < plantRecords.length; i++) {
            for (let j = 0; j < productRecords.length; j++) {
                const totalQty = Math.floor(Math.random() * 300) + 50; // Random stock between 50-350
                const reservedQty = Math.floor(totalQty * 0.3); // 30% reserved
                
                stockData.push({
                    ID: cds.utils.uuid(),
                    plant_ID: plantRecords[i].ID,
                    product_ID: productRecords[j].ID,
                    quantity: totalQty,
                    reservedQty: reservedQty,
                    availableQty: totalQty - reservedQty,
                    lastUpdated: new Date(),
                    createdAt: new Date(),
                    createdBy: 'SYSTEM'
                });
            }
        }
        
        await db.run(INSERT.into('demand.supply.reconciliation.Stock').entries(stockData));
        
        console.log('Data seeding completed successfully!');
        console.log(`Seeded ${plants.length} plants`);
        console.log(`Seeded ${products.length} products`);
        console.log(`Seeded ${vendors.length} vendors`);
        console.log(`Seeded ${demandData.length} demand records`);
        console.log(`Seeded ${supplyData.length} supply records`);
        console.log(`Seeded ${stockData.length} stock records`);
        
    } catch (error) {
        console.error('Error seeding data:', error);
        throw error;
    }
}

// Export for use in other scripts
module.exports = { seedData };

// Run if called directly
if (require.main === module) {
    seedData().then(() => {
        console.log('Seeding completed');
        process.exit(0);
    }).catch((error) => {
        console.error('Seeding failed:', error);
        process.exit(1);
    });
}

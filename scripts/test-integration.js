const axios = require('axios');

// Configuration
const CAP_BASE_URL = process.env.CAP_BASE_URL || 'http://localhost:4004';
const AI_BASE_URL = process.env.AI_BASE_URL || 'http://localhost:5000';

async function testIntegration() {
    console.log('🧪 Starting Integration Tests...\n');
    
    let passed = 0;
    let failed = 0;
    
    // Test 1: AI Service Health Check
    try {
        console.log('1️⃣ Testing AI Service Health...');
        const response = await axios.get(`${AI_BASE_URL}/health`);
        if (response.status === 200 && response.data.status === 'healthy') {
            console.log('✅ AI Service is healthy');
            passed++;
        } else {
            console.log('❌ AI Service health check failed');
            failed++;
        }
    } catch (error) {
        console.log('❌ AI Service is not accessible:', error.message);
        failed++;
    }
    
    // Test 2: CAP Service Metadata
    try {
        console.log('\n2️⃣ Testing CAP Service Metadata...');
        const response = await axios.get(`${CAP_BASE_URL}/reconciliation/$metadata`);
        if (response.status === 200) {
            console.log('✅ CAP Service metadata is accessible');
            passed++;
        } else {
            console.log('❌ CAP Service metadata failed');
            failed++;
        }
    } catch (error) {
        console.log('❌ CAP Service is not accessible:', error.message);
        failed++;
    }
    
    // Test 3: AI Recommendation Generation
    try {
        console.log('\n3️⃣ Testing AI Recommendation Generation...');
        const testData = {
            plant: {
                code: 'P001',
                name: 'Test Plant',
                capacity: 1000
            },
            product: {
                code: 'PROD001',
                name: 'Test Product'
            },
            demand: 500,
            supply: 300,
            stock: 100,
            variance: -100,
            context: {
                urgency: 'HIGH',
                reconciliationDate: '2024-01-15'
            }
        };
        
        const response = await axios.post(`${AI_BASE_URL}/recommend`, testData);
        if (response.status === 200 && response.data.success && response.data.recommendations) {
            console.log('✅ AI Recommendation generation successful');
            console.log(`   Generated ${response.data.recommendations.length} recommendations`);
            passed++;
        } else {
            console.log('❌ AI Recommendation generation failed');
            failed++;
        }
    } catch (error) {
        console.log('❌ AI Recommendation generation error:', error.message);
        failed++;
    }
    
    // Test 4: CAP Master Data Access
    try {
        console.log('\n4️⃣ Testing CAP Master Data Access...');
        const response = await axios.get(`${CAP_BASE_URL}/reconciliation/Plants`);
        if (response.status === 200) {
            const plants = response.data.value || response.data;
            console.log(`✅ CAP Master Data accessible (${plants.length} plants found)`);
            passed++;
        } else {
            console.log('❌ CAP Master Data access failed');
            failed++;
        }
    } catch (error) {
        console.log('❌ CAP Master Data access error:', error.message);
        failed++;
    }
    
    // Test 5: End-to-End Reconciliation (if data exists)
    try {
        console.log('\n5️⃣ Testing End-to-End Reconciliation...');
        const reconciliationData = {
            plantCode: '',
            productCode: '',
            reconciliationDate: new Date().toISOString().split('T')[0]
        };
        
        const response = await axios.post(`${CAP_BASE_URL}/reconciliation/runReconciliation`, reconciliationData);
        if (response.status === 200 && response.data.success !== false) {
            console.log('✅ End-to-End Reconciliation successful');
            console.log(`   Reconciliation ID: ${response.data.reconciliationId}`);
            console.log(`   Recommendations: ${response.data.recommendationsCount}`);
            passed++;
        } else {
            console.log('⚠️ End-to-End Reconciliation completed with warnings:', response.data.message);
            // This might be expected if no data exists
            passed++;
        }
    } catch (error) {
        console.log('❌ End-to-End Reconciliation error:', error.message);
        failed++;
    }
    
    // Test 6: UI5 App Accessibility
    try {
        console.log('\n6️⃣ Testing UI5 App Accessibility...');
        const response = await axios.get(`${CAP_BASE_URL}/app/reconciliation-dashboard/webapp/index.html`);
        if (response.status === 200 && response.data.includes('Demand-Supply Reconciliation System')) {
            console.log('✅ UI5 App is accessible');
            passed++;
        } else {
            console.log('❌ UI5 App accessibility failed');
            failed++;
        }
    } catch (error) {
        console.log('❌ UI5 App accessibility error:', error.message);
        failed++;
    }
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 All tests passed! The system is ready for use.');
        return true;
    } else {
        console.log('\n⚠️ Some tests failed. Please check the services and configuration.');
        return false;
    }
}

// Additional utility functions
async function testAIModels() {
    try {
        console.log('\n🤖 Testing Available AI Models...');
        const response = await axios.get(`${AI_BASE_URL}/models`);
        if (response.status === 200) {
            const models = response.data.models || [];
            console.log(`Available models: ${models.length}`);
            models.forEach(model => {
                console.log(`  - ${model.name} (${model.size || 'Unknown size'})`);
            });
        }
    } catch (error) {
        console.log('Could not retrieve AI models:', error.message);
    }
}

async function testDashboardKPIs() {
    try {
        console.log('\n📊 Testing Dashboard KPIs...');
        const kpiData = {
            plantCode: '',
            dateFrom: '2024-01-01',
            dateTo: new Date().toISOString().split('T')[0]
        };
        
        const response = await axios.post(`${CAP_BASE_URL}/reconciliation/getDashboardKPIs`, kpiData);
        if (response.status === 200) {
            console.log('✅ Dashboard KPIs accessible');
            console.log('   KPI Data:', JSON.stringify(response.data, null, 2));
        }
    } catch (error) {
        console.log('Dashboard KPIs test failed:', error.message);
    }
}

// Run tests
if (require.main === module) {
    testIntegration()
        .then(async (success) => {
            // Run additional tests if basic integration works
            if (success) {
                await testAIModels();
                await testDashboardKPIs();
            }
            
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('Integration test failed:', error);
            process.exit(1);
        });
}

module.exports = { testIntegration, testAIModels, testDashboardKPIs };

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import logging
import time
import random
import re
from datetime import datetime
import os
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# AI Model Configuration - Together AI + BAS AI Fallback
USE_TOGETHER_AI = os.getenv('USE_TOGETHER_AI', 'true').lower() == 'true'
USE_BAS_AI = os.getenv('USE_BAS_AI', 'true').lower() == 'true'  # Always available as fallback

# Together AI Configuration (Primary AI Model)
TOGETHER_AI_API_KEY = os.getenv('TOGETHER_AI_API_KEY', '')
TOGETHER_AI_MODEL = os.getenv('TOGETHER_AI_MODEL', 'togethercomputer/RedPajama-INCITE-Chat-3B-v1')
TOGETHER_AI_BASE_URL = 'https://api.together.xyz/v1'

class TogetherAIGenerator:
    """
    Together AI Generator - Premium free AI model integration
    Primary: Together AI (RedPajama-3B, Llama-2-7B, etc.)
    Fallback: BAS AI Generator
    """

    def __init__(self):
        self.model_priority = [
            'together_ai',        # Primary: Best quality free AI
            'bas_ai_fallback'     # Fallback: Always works
        ]

        # Business context for AI prompts
        self.system_prompt = """You are an expert supply chain analyst and AI assistant for demand-supply reconciliation.
        Your role is to analyze inventory data and provide actionable recommendations for:
        - Procurement requests for shortages
        - Production orders for capacity optimization
        - Stock reallocation for surplus management

        Always provide:
        1. Clear, actionable recommendations
        2. Confidence scores (0.5-0.95)
        3. Cost estimates in USD
        4. Time estimates in days
        5. Risk assessments
        6. Business reasoning

        Respond in JSON format with structured recommendations."""

    def generate_intelligent_recommendation(self, data):
        """Generate AI recommendations using available models"""
        try:
            # Try each AI model in priority order
            for model_type in self.model_priority:
                if self._is_model_available(model_type):
                    logger.info(f"Using AI model: {model_type}")
                    return self._generate_with_model(model_type, data)

            # Fallback to BAS AI system
            logger.warning("No AI models available, using BAS AI fallback")
            return self._generate_bas_ai_fallback(data)

        except Exception as e:
            logger.error(f"AI generation error: {e}")
            return self._generate_bas_ai_fallback(data)

    def _is_model_available(self, model_type):
        """Check if specific AI model is available"""
        if model_type == 'together_ai' and USE_TOGETHER_AI and TOGETHER_AI_API_KEY:
            return True
        elif model_type == 'bas_ai_fallback':
            return True  # BAS AI is always available
        return False

    def _generate_with_model(self, model_type, data):
        """Generate recommendations with specific AI model"""
        prompt = self._create_business_prompt(data)

        if model_type == 'together_ai':
            return self._call_together_ai_api(prompt, data)
        elif model_type == 'bas_ai_fallback':
            return self._generate_bas_ai_fallback(data)

        return self._generate_bas_ai_fallback(data)

    def _create_business_prompt(self, data):
        """Create detailed business context prompt"""
        plant = data.get('plant', {})
        product = data.get('product', {})
        variance = data.get('variance', 0)
        demand = data.get('demand', 0)
        supply = data.get('supply', 0)
        stock = data.get('stock', 0)
        context = data.get('context', {})

        prompt = f"""
        SUPPLY CHAIN ANALYSIS REQUEST:

        Plant: {plant.get('name', 'Unknown')} (Code: {plant.get('code', 'N/A')})
        Capacity: {plant.get('capacity', 0)} units

        Product: {product.get('name', 'Unknown')} (Code: {product.get('code', 'N/A')})

        Current Situation:
        - Demand: {demand} units
        - Supply: {supply} units
        - Current Stock: {stock} units
        - Variance: {variance} units ({'shortage' if variance < 0 else 'surplus' if variance > 0 else 'balanced'})

        Context:
        - Date: {context.get('reconciliationDate', 'Today')}
        - Urgency: {context.get('urgency', 'MEDIUM')}

        Please analyze this situation and provide 2-3 actionable recommendations in JSON format:
        {{
            "recommendations": [
                {{
                    "type": "PROCUREMENT_REQUEST|PRODUCTION_ORDER|STOCK_REALLOCATION",
                    "description": "Detailed recommendation description",
                    "priority": "HIGH|MEDIUM|LOW",
                    "confidence": 0.85,
                    "quantity": 30,
                    "estimatedCost": 1500,
                    "estimatedTime": 7,
                    "reasoning": "Business reasoning for this recommendation",
                    "risks": "Potential risks and mitigation strategies",
                    "aiModel": "model_name"
                }}
            ]
        }}
        """
        return prompt

    def _call_together_ai_api(self, prompt, data):
        """Call Together AI API - Best free AI model"""
        try:
            import requests

            headers = {
                "Authorization": f"Bearer {TOGETHER_AI_API_KEY}",
                "Content-Type": "application/json"
            }

            # Use the best free model from Together AI
            payload = {
                "model": TOGETHER_AI_MODEL,
                "messages": [
                    {
                        "role": "system",
                        "content": self.system_prompt
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 800,
                "top_p": 0.9,
                "repetition_penalty": 1.1
            }

            logger.info(f"Calling Together AI with model: {TOGETHER_AI_MODEL}")
            response = requests.post(
                f"{TOGETHER_AI_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                ai_text = result['choices'][0]['message']['content']
                logger.info("Together AI response received successfully")
                return self._parse_ai_response(ai_text, f'Together-AI-{TOGETHER_AI_MODEL.split("/")[-1]}')
            else:
                logger.error(f"Together AI API error: {response.status_code} - {response.text}")
                return self._generate_bas_ai_fallback(data)

        except Exception as e:
            logger.error(f"Together AI API call failed: {e}")
            return self._generate_bas_ai_fallback(data)

    def _generate_bas_ai_fallback(self, data):
        """Generate recommendations using BAS AI as fallback"""
        bas_generator = BASAIGenerator()
        recommendations = bas_generator.generate_intelligent_recommendation(data)

        # Enhance with AI-like features
        for rec in recommendations:
            rec['aiModel'] = 'BAS-AI-Fallback-v2.0'
            rec['confidence'] = min(0.95, rec.get('confidence', 0.7) + 0.05)  # Slight boost

        return recommendations

    def _parse_ai_response(self, ai_text, model_name):
        """Parse Together AI response and structure it properly"""
        try:
            # Try to extract JSON from AI response
            json_match = re.search(r'\{.*\}', ai_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)

                if 'recommendations' in parsed:
                    recommendations = parsed['recommendations']
                    for rec in recommendations:
                        rec['aiModel'] = model_name
                    return recommendations

            # If no valid JSON, create structured response from text
            return self._create_structured_from_text(ai_text, model_name)

        except Exception as e:
            logger.error(f"Failed to parse Together AI response: {e}")
            return self._generate_bas_ai_fallback({})

    def _create_structured_from_text(self, text, model_name):
        """Create structured recommendations from Together AI text"""
        recommendations = []

        # Extract key phrases and create recommendations
        if 'procurement' in text.lower() or 'purchase' in text.lower() or 'buy' in text.lower():
            recommendations.append({
                'type': 'PROCUREMENT_REQUEST',
                'description': f"Together AI Analysis: {text[:200]}...",
                'priority': 'HIGH' if 'urgent' in text.lower() else 'MEDIUM',
                'confidence': 0.88,
                'quantity': self._extract_number(text, 'quantity'),
                'estimatedCost': self._extract_number(text, 'cost'),
                'estimatedTime': self._extract_number(text, 'time'),
                'reasoning': 'Together AI-generated recommendation based on advanced supply chain analysis',
                'risks': 'AI-identified procurement risks and mitigation strategies',
                'aiModel': model_name
            })

        if 'production' in text.lower() or 'manufacture' in text.lower():
            recommendations.append({
                'type': 'PRODUCTION_ORDER',
                'description': f"Together AI Production Recommendation: {text[:200]}...",
                'priority': 'MEDIUM',
                'confidence': 0.82,
                'quantity': self._extract_number(text, 'quantity'),
                'estimatedCost': self._extract_number(text, 'cost'),
                'estimatedTime': self._extract_number(text, 'time'),
                'reasoning': 'Together AI-generated production optimization strategy',
                'risks': 'AI-assessed production capacity and quality considerations',
                'aiModel': model_name
            })

        return recommendations if recommendations else self._generate_bas_ai_fallback({})

    def _extract_number(self, text, context):
        """Extract numbers from text based on context"""
        # Default values based on context
        defaults = {
            'quantity': 25,
            'cost': 1200,
            'time': 7
        }

        # Look for numbers in the text
        numbers = re.findall(r'\d+', text)
        if numbers:
            return int(numbers[0])

        return defaults.get(context, 10)

class BASAIGenerator:
    """
    Advanced AI Generator for BAS - Simulates intelligent AI responses
    without requiring external AI services
    """

    def __init__(self):
        self.templates = {
            'shortage': [
                "Immediate procurement of {quantity} units of {product} is recommended for {plant}. Current shortage of {variance} units poses {risk_level} risk to production schedule.",
                "Critical supply gap detected for {product} at {plant}. Recommend urgent procurement of {quantity} units to maintain operational continuity.",
                "Production bottleneck identified: {product} shortage at {plant}. Suggest expedited procurement of {quantity} units with priority shipping.",
                "Supply chain disruption alert: {plant} requires immediate restocking of {product}. Recommend procurement of {quantity} units within 48 hours."
            ],
            'surplus': [
                "Excess inventory of {quantity} units detected for {product} at {plant}. Recommend reallocation to high-demand locations to optimize working capital.",
                "Surplus stock optimization opportunity: {product} at {plant} shows {variance} units excess. Consider redistribution to plants with higher demand.",
                "Inventory rebalancing recommended: {plant} has surplus {product} inventory. Suggest transferring {quantity} units to deficit locations.",
                "Working capital optimization: Redistribute {quantity} units of {product} from {plant} to reduce carrying costs and improve inventory turnover."
            ],
            'production': [
                "Production capacity analysis suggests increasing {product} manufacturing at {plant} by {quantity} units to address demand-supply gap.",
                "Manufacturing optimization: Scale up {product} production at {plant} to produce additional {quantity} units and reduce dependency on external suppliers.",
                "Internal production recommendation: Utilize available capacity at {plant} to manufacture {quantity} units of {product} and improve margins.",
                "Strategic production increase: Boost {product} output at {plant} by {quantity} units to achieve supply chain resilience."
            ]
        }

        self.risk_factors = {
            'HIGH': ['critical', 'severe', 'urgent', 'immediate'],
            'MEDIUM': ['moderate', 'significant', 'notable', 'considerable'],
            'LOW': ['minor', 'slight', 'manageable', 'minimal']
        }

        self.reasoning_patterns = [
            "Based on historical demand patterns and current inventory levels",
            "Considering production capacity constraints and lead times",
            "Analyzing supply chain risks and cost optimization opportunities",
            "Evaluating market conditions and supplier reliability factors",
            "Taking into account seasonal demand variations and safety stock requirements"
        ]

        self.risk_assessments = [
            "Supplier lead time variability may impact delivery schedule",
            "Transportation costs and logistics coordination required",
            "Quality control and inspection processes must be maintained",
            "Production scheduling adjustments may be necessary",
            "Working capital impact should be monitored closely"
        ]

    def generate_intelligent_recommendation(self, data):
        """Generate contextually intelligent recommendations"""
        plant = data.get('plant', {})
        product = data.get('product', {})
        variance = data.get('variance', 0)
        demand = data.get('demand', 0)
        supply = data.get('supply', 0)
        stock = data.get('stock', 0)
        context = data.get('context', {})

        recommendations = []

        # Determine scenario type
        if variance < -5:  # Significant shortage
            recommendations.extend(self._generate_shortage_recommendations(data))
        elif variance > 5:  # Significant surplus
            recommendations.extend(self._generate_surplus_recommendations(data))
        elif abs(variance) <= 5 and demand > 0:  # Balanced but could optimize
            recommendations.extend(self._generate_optimization_recommendations(data))

        # Add production recommendations if plant has capacity
        if plant.get('capacity', 0) > supply:
            recommendations.extend(self._generate_production_recommendations(data))

        return recommendations[:3]  # Return top 3 recommendations

    def _generate_shortage_recommendations(self, data):
        """Generate recommendations for shortage scenarios"""
        recommendations = []
        plant_name = data.get('plant', {}).get('name', 'Plant')
        product_name = data.get('product', {}).get('name', 'Product')
        variance = abs(data.get('variance', 0))

        # Procurement recommendation
        template = random.choice(self.templates['shortage'])
        description = template.format(
            quantity=int(variance * 1.2),  # Add safety buffer
            product=product_name,
            plant=plant_name,
            variance=int(variance),
            risk_level=self._determine_risk_level(variance)
        )

        recommendations.append({
            'type': 'PROCUREMENT_REQUEST',
            'description': description,
            'priority': self._calculate_priority(variance, data.get('context', {})),
            'confidence': self._calculate_confidence('procurement', data),
            'quantity': int(variance * 1.2),
            'estimatedCost': self._estimate_cost('procurement', variance, data),
            'estimatedTime': self._estimate_time('procurement', variance),
            'reasoning': random.choice(self.reasoning_patterns),
            'risks': random.choice(self.risk_assessments),
            'aiModel': 'BAS_AI_GENERATOR_v1.0'
        })

        return recommendations

    def _generate_surplus_recommendations(self, data):
        """Generate recommendations for surplus scenarios"""
        recommendations = []
        plant_name = data.get('plant', {}).get('name', 'Plant')
        product_name = data.get('product', {}).get('name', 'Product')
        variance = data.get('variance', 0)

        template = random.choice(self.templates['surplus'])
        description = template.format(
            quantity=int(variance),
            product=product_name,
            plant=plant_name,
            variance=int(variance)
        )

        recommendations.append({
            'type': 'STOCK_REALLOCATION',
            'description': description,
            'priority': 'MEDIUM',
            'confidence': self._calculate_confidence('reallocation', data),
            'quantity': int(variance),
            'estimatedCost': self._estimate_cost('reallocation', variance, data),
            'estimatedTime': self._estimate_time('reallocation', variance),
            'reasoning': random.choice(self.reasoning_patterns),
            'risks': random.choice(self.risk_assessments),
            'aiModel': 'BAS_AI_GENERATOR_v1.0'
        })

        return recommendations

    def _generate_production_recommendations(self, data):
        """Generate production-based recommendations"""
        recommendations = []
        plant = data.get('plant', {})
        product_name = data.get('product', {}).get('name', 'Product')
        plant_name = plant.get('name', 'Plant')
        variance = abs(data.get('variance', 0))
        capacity = plant.get('capacity', 1000)
        current_supply = data.get('supply', 0)

        # Only recommend if there's available capacity
        if capacity > current_supply and variance > 0:
            available_capacity = min(capacity - current_supply, variance * 1.5)

            template = random.choice(self.templates['production'])
            description = template.format(
                quantity=int(available_capacity),
                product=product_name,
                plant=plant_name
            )

            recommendations.append({
                'type': 'PRODUCTION_ORDER',
                'description': description,
                'priority': 'MEDIUM',
                'confidence': self._calculate_confidence('production', data),
                'quantity': int(available_capacity),
                'estimatedCost': self._estimate_cost('production', available_capacity, data),
                'estimatedTime': self._estimate_time('production', available_capacity),
                'reasoning': random.choice(self.reasoning_patterns),
                'risks': random.choice(self.risk_assessments),
                'aiModel': 'BAS_AI_GENERATOR_v1.0'
            })

        return recommendations

    def _generate_optimization_recommendations(self, data):
        """Generate optimization recommendations for balanced scenarios"""
        recommendations = []

        # Add preventive recommendations
        plant_name = data.get('plant', {}).get('name', 'Plant')
        product_name = data.get('product', {}).get('name', 'Product')

        recommendations.append({
            'type': 'INVENTORY_OPTIMIZATION',
            'description': f"Implement predictive analytics for {product_name} at {plant_name} to prevent future imbalances and optimize safety stock levels.",
            'priority': 'LOW',
            'confidence': 0.7,
            'quantity': 0,
            'estimatedCost': 500,
            'estimatedTime': 14,
            'reasoning': "Proactive inventory management to prevent future disruptions",
            'risks': "Implementation requires staff training and system integration",
            'aiModel': 'BAS_AI_GENERATOR_v1.0'
        })

        return recommendations

    def _determine_risk_level(self, variance):
        """Determine risk level based on variance magnitude"""
        if abs(variance) > 50:
            return 'HIGH'
        elif abs(variance) > 20:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _calculate_priority(self, variance, context):
        """Calculate priority based on variance and context"""
        urgency = context.get('urgency', 'MEDIUM')

        if abs(variance) > 50 or urgency == 'HIGH':
            return 'HIGH'
        elif abs(variance) > 20 or urgency == 'MEDIUM':
            return 'MEDIUM'
        else:
            return 'LOW'

    def _calculate_confidence(self, rec_type, data):
        """Calculate confidence score based on data quality and scenario"""
        base_confidence = 0.7

        # Adjust based on data completeness
        if all(key in data for key in ['plant', 'product', 'demand', 'supply']):
            base_confidence += 0.1

        # Adjust based on variance magnitude (more extreme = higher confidence)
        variance = abs(data.get('variance', 0))
        if variance > 30:
            base_confidence += 0.15
        elif variance > 10:
            base_confidence += 0.05

        # Adjust based on recommendation type
        type_adjustments = {
            'procurement': 0.1,
            'production': -0.05,
            'reallocation': 0.05
        }
        base_confidence += type_adjustments.get(rec_type, 0)

        # Add some randomness to simulate real AI variability
        base_confidence += random.uniform(-0.05, 0.05)

        return min(0.95, max(0.5, base_confidence))

    def _estimate_cost(self, rec_type, quantity, data):
        """Estimate cost based on recommendation type and quantity"""
        base_costs = {
            'procurement': 45,
            'production': 35,
            'reallocation': 8
        }

        base_cost = base_costs.get(rec_type, 40)
        total_cost = base_cost * quantity

        # Add complexity factors
        if data.get('context', {}).get('urgency') == 'HIGH':
            total_cost *= 1.3  # Rush order premium

        return int(total_cost)

    def _estimate_time(self, rec_type, quantity):
        """Estimate time based on recommendation type and quantity"""
        base_times = {
            'procurement': 7,
            'production': 12,
            'reallocation': 3
        }

        base_time = base_times.get(rec_type, 7)

        # Adjust for quantity
        if quantity > 100:
            base_time += 3
        elif quantity > 50:
            base_time += 1

        return base_time

class RecommendationEngine:
    
    def __init__(self):
        self.bas_ai = BASAIGenerator()  # Initialize BAS AI Generator
        self.together_ai = TogetherAIGenerator()  # Initialize Together AI Generator
        
    def generate_recommendations(self, data):
        """
        Generate reconciliation recommendations based on demand-supply data
        """
        try:
            # Extract data
            plant = data.get('plant', {})
            product = data.get('product', {})
            demand = float(data.get('demand', 0))
            supply = float(data.get('supply', 0))
            stock = float(data.get('stock', 0))
            variance = float(data.get('variance', 0))
            context = data.get('context', {})
            
            # Create prompt for AI
            prompt = self.create_reconciliation_prompt(plant, product, demand, supply, stock, variance, context)
            
            # Get AI recommendations with priority system
            start_time = time.time()

            # Try Together AI first (highest quality free AI)
            if USE_TOGETHER_AI:
                logger.info("Using Together AI Premium Model")
                recommendations = self.together_ai.generate_intelligent_recommendation(data)
                processing_time = int((time.time() - start_time) * 1000)

                ai_model = recommendations[0].get('aiModel', 'Together-AI') if recommendations else 'Together-AI'

                return {
                    'success': True,
                    'recommendations': recommendations,
                    'ai_model': ai_model,
                    'processing_time_ms': processing_time,
                    'confidence_avg': sum(r.get('confidence', 0.7) for r in recommendations) / len(recommendations) if recommendations else 0.7,
                    'ai_type': 'premium_ai',
                    'provider': 'Together AI'
                }

            # Fallback to BAS AI Generator
            else:
                logger.info("Using BAS AI Generator (Fallback)")
                recommendations = self.bas_ai.generate_intelligent_recommendation(data)
                processing_time = int((time.time() - start_time) * 1000)

                return {
                    'success': True,
                    'recommendations': recommendations,
                    'ai_model': 'BAS_AI_GENERATOR_v2.0',
                    'processing_time_ms': processing_time,
                    'confidence_avg': sum(r.get('confidence', 0.7) for r in recommendations) / len(recommendations) if recommendations else 0.7,
                    'ai_type': 'rule_based_fallback'
                }
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'recommendations': []
            }
    
    def create_reconciliation_prompt(self, plant, product, demand, supply, stock, variance, context):
        """
        Create a detailed prompt for the AI model
        """
        urgency = context.get('urgency', 'MEDIUM')
        reconciliation_date = context.get('reconciliationDate', datetime.now().strftime('%Y-%m-%d'))
        
        prompt = f"""
You are an expert manufacturing supply chain analyst. Analyze the following demand-supply situation and provide specific, actionable recommendations.

SITUATION ANALYSIS:
- Plant: {plant.get('name', 'Unknown')} (Code: {plant.get('code', 'N/A')})
- Product: {product.get('name', 'Unknown')} (Code: {product.get('code', 'N/A')})
- Current Demand: {demand} units
- Available Supply: {supply} units
- Current Stock: {stock} units
- Variance: {variance} units ({'SHORTAGE' if variance < 0 else 'SURPLUS' if variance > 0 else 'BALANCED'})
- Plant Capacity: {plant.get('capacity', 'Unknown')} units
- Urgency Level: {urgency}
- Analysis Date: {reconciliation_date}

CONTEXT:
- Negative variance indicates shortage (demand > supply + stock)
- Positive variance indicates surplus (supply + stock > demand)
- Consider lead times, costs, and operational constraints

REQUIRED OUTPUT FORMAT (JSON):
{{
  "analysis": "Brief analysis of the situation",
  "recommendations": [
    {{
      "type": "STOCK_REALLOCATION|PROCUREMENT_REQUEST|PRODUCTION_ORDER",
      "description": "Detailed description of the recommended action",
      "priority": "HIGH|MEDIUM|LOW",
      "confidence": 0.85,
      "quantity": 100,
      "estimatedCost": 5000,
      "estimatedTime": 7,
      "reasoning": "Why this recommendation makes sense",
      "risks": "Potential risks or considerations"
    }}
  ]
}}

GUIDELINES:
1. For SHORTAGE (negative variance):
   - Consider PROCUREMENT_REQUEST if external sourcing is needed
   - Consider PRODUCTION_ORDER if plant has capacity
   - Consider STOCK_REALLOCATION from other plants with surplus

2. For SURPLUS (positive variance):
   - Consider STOCK_REALLOCATION to plants with shortages
   - Consider reducing future production/procurement

3. Confidence scoring:
   - 0.9-1.0: Very high confidence, auto-trigger recommended
   - 0.7-0.89: High confidence, minimal approval needed
   - 0.5-0.69: Medium confidence, requires approval
   - Below 0.5: Low confidence, manual review required

4. Priority levels:
   - HIGH: Critical shortages, customer impact
   - MEDIUM: Moderate impact, efficiency improvements
   - LOW: Minor optimizations

Provide 1-3 recommendations maximum. Focus on the most impactful actions.
"""
        
        return prompt
    

    
    def generate_fallback_recommendations(self, plant, product, demand, supply, stock, variance, context):
        """
        Generate structured fallback recommendations
        """
        recommendations = []
        
        if variance < 0:  # Shortage
            shortage_qty = abs(variance)
            
            # Procurement recommendation
            recommendations.append({
                "type": "PROCUREMENT_REQUEST",
                "description": f"Procure {shortage_qty} units of {product.get('name', 'product')} for {plant.get('name', 'plant')}",
                "priority": "HIGH" if shortage_qty > demand * 0.2 else "MEDIUM",
                "confidence": 0.7,
                "quantity": shortage_qty,
                "estimatedCost": shortage_qty * 50,  # Estimated cost per unit
                "estimatedTime": 7,
                "reasoning": "Direct procurement to address shortage",
                "risks": "Lead time and supplier availability"
            })
            
            # Production recommendation if plant has capacity
            if plant.get('capacity', 0) > 0:
                recommendations.append({
                    "type": "PRODUCTION_ORDER",
                    "description": f"Produce {shortage_qty} units of {product.get('name', 'product')} at {plant.get('name', 'plant')}",
                    "priority": "MEDIUM",
                    "confidence": 0.65,
                    "quantity": shortage_qty,
                    "estimatedCost": shortage_qty * 40,
                    "estimatedTime": 10,
                    "reasoning": "Internal production to reduce dependency on external suppliers",
                    "risks": "Production capacity and material availability"
                })
        
        elif variance > 0:  # Surplus
            recommendations.append({
                "type": "STOCK_REALLOCATION",
                "description": f"Reallocate {variance} units of {product.get('name', 'product')} from {plant.get('name', 'plant')} to deficit locations",
                "priority": "MEDIUM",
                "confidence": 0.75,
                "quantity": variance,
                "estimatedCost": variance * 5,  # Transport cost
                "estimatedTime": 3,
                "reasoning": "Optimize inventory distribution across plants",
                "risks": "Transportation logistics and timing"
            })
        
        return json.dumps({
            "analysis": f"Fallback analysis: {'Shortage' if variance < 0 else 'Surplus' if variance > 0 else 'Balanced'} situation detected",
            "recommendations": recommendations
        })
    
    def parse_ai_response(self, ai_response, variance):
        """
        Parse AI response and structure recommendations
        """
        try:
            # Try to extract JSON from the response
            if '{' in ai_response and '}' in ai_response:
                start = ai_response.find('{')
                end = ai_response.rfind('}') + 1
                json_str = ai_response[start:end]
                
                parsed = json.loads(json_str)
                recommendations = parsed.get('recommendations', [])
                
                # Enhance recommendations with additional metadata
                for rec in recommendations:
                    rec['aiModel'] = 'FALLBACK_LOGIC'
                    rec['aiPrompt'] = 'Generated by fallback analysis'
                    rec['aiResponse'] = ai_response[:500]  # Truncate for storage
                    
                    # Ensure required fields
                    if 'confidence' not in rec:
                        rec['confidence'] = 0.6
                    if 'estimatedCost' not in rec:
                        rec['estimatedCost'] = 1000
                    if 'estimatedTime' not in rec:
                        rec['estimatedTime'] = 5
                
                return recommendations
            else:
                # If no JSON found, create a basic recommendation
                return self.create_basic_recommendation(variance)
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {str(e)}")
            return self.create_basic_recommendation(variance)
    
    def create_basic_recommendation(self, variance):
        """
        Create a basic recommendation when parsing fails
        """
        if variance < 0:
            return [{
                "type": "PROCUREMENT_REQUEST",
                "description": f"Address shortage of {abs(variance)} units through procurement",
                "priority": "MEDIUM",
                "confidence": 0.6,
                "quantity": abs(variance),
                "estimatedCost": abs(variance) * 50,
                "estimatedTime": 7,
                "aiModel": "BASIC_LOGIC",
                "aiPrompt": "Basic recommendation due to parsing failure",
                "aiResponse": "Generated basic procurement recommendation"
            }]
        elif variance > 0:
            return [{
                "type": "STOCK_REALLOCATION",
                "description": f"Reallocate surplus of {variance} units to other locations",
                "priority": "LOW",
                "confidence": 0.6,
                "quantity": variance,
                "estimatedCost": variance * 5,
                "estimatedTime": 3,
                "aiModel": "BASIC_LOGIC",
                "aiPrompt": "Basic recommendation due to parsing failure",
                "aiResponse": "Generated basic reallocation recommendation"
            }]
        else:
            return []

# Initialize recommendation engine
recommendation_engine = RecommendationEngine()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'model': TOGETHER_AI_MODEL,
        'together_ai_enabled': USE_TOGETHER_AI,
        'bas_ai_enabled': USE_BAS_AI
    })

@app.route('/recommend', methods=['POST'])
def recommend():
    """
    Main recommendation endpoint
    """
    start_time = time.time()
    
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Request must be JSON'
            }), 400
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['plant', 'product', 'demand', 'supply', 'stock', 'variance']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Generate recommendations
        result = recommendation_engine.generate_recommendations(data)
        
        # Add processing time
        processing_time = int((time.time() - start_time) * 1000)
        result['processing_time_ms'] = processing_time
        
        logger.info(f"Generated {len(result.get('recommendations', []))} recommendations in {processing_time}ms")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in recommend endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'recommendations': []
        }), 500

@app.route('/models', methods=['GET'])
def list_models():
    """List available AI models"""
    models = []

    if USE_TOGETHER_AI and TOGETHER_AI_API_KEY:
        models.append({
            'name': TOGETHER_AI_MODEL,
            'provider': 'Together AI',
            'status': 'active',
            'type': 'premium_ai'
        })

    if USE_BAS_AI:
        models.append({
            'name': 'BAS_AI_GENERATOR_v2.0',
            'provider': 'BAS',
            'status': 'active',
            'type': 'rule_based'
        })

    return jsonify({'models': models})

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting Flask AI service on port {port}")
    logger.info(f"Together AI Model: {TOGETHER_AI_MODEL}")
    logger.info(f"Together AI enabled: {USE_TOGETHER_AI}")
    logger.info(f"BAS AI enabled: {USE_BAS_AI}")
    
    app.run(host='0.0.0.0', port=port, debug=debug)

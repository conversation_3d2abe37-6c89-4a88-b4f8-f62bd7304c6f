#!/usr/bin/env node

/**
 * GenAI Integration Test Script
 * This demonstrates how the GenAI functionality works in the reconciliation system
 */

const axios = require('axios');

// Configuration
const AI_SERVICE_URL = 'http://localhost:5000';

// Sample reconciliation data that would trigger GenAI recommendations
const sampleReconciliationData = {
    plant: {
        code: "P001",
        name: "Manufacturing Plant North",
        capacity: 10000
    },
    product: {
        code: "PROD001", 
        name: "Product A"
    },
    demand: 100,
    supply: 80,
    stock: 50,
    variance: -20, // Shortage of 20 units
    context: {
        reconciliationDate: "2024-01-15",
        urgency: "HIGH"
    }
};

async function testGenAIRecommendations() {
    console.log('🤖 Testing GenAI Recommendation Engine');
    console.log('=====================================\n');
    
    console.log('📊 Input Data:');
    console.log(JSON.stringify(sampleReconciliationData, null, 2));
    console.log('\n');
    
    try {
        // Test health endpoint first
        console.log('🔍 Checking AI service health...');
        const healthResponse = await axios.get(`${AI_SERVICE_URL}/health`);
        console.log('✅ AI Service Status:', healthResponse.data);
        console.log('\n');
        
        // Call the recommendation endpoint
        console.log('🧠 Generating AI Recommendations...');
        const response = await axios.post(`${AI_SERVICE_URL}/recommend`, sampleReconciliationData, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ AI Recommendations Generated:');
        console.log(JSON.stringify(response.data, null, 2));
        
        // Analyze the recommendations
        if (response.data.success && response.data.recommendations) {
            console.log('\n📋 Recommendation Analysis:');
            response.data.recommendations.forEach((rec, index) => {
                console.log(`\n${index + 1}. ${rec.type}:`);
                console.log(`   Description: ${rec.description}`);
                console.log(`   Priority: ${rec.priority}`);
                console.log(`   Confidence: ${(rec.confidence * 100).toFixed(1)}%`);
                console.log(`   Estimated Cost: $${rec.estimatedCost}`);
                console.log(`   Estimated Time: ${rec.estimatedTime} days`);
                
                // Determine if this would be auto-triggered
                const autoTrigger = rec.confidence >= 0.8;
                console.log(`   Auto-Trigger: ${autoTrigger ? '✅ YES' : '❌ NO (requires approval)'}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error testing GenAI:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 The AI service is not running. To start it:');
            console.log('   cd flask-api && python app.py');
        }
        
        console.log('\n🔄 Demonstrating Fallback Logic:');
        const fallbackRecommendations = generateFallbackRecommendations(sampleReconciliationData);
        console.log(JSON.stringify(fallbackRecommendations, null, 2));
    }
}

function generateFallbackRecommendations(data) {
    const recommendations = [];
    const { variance, plant, product } = data;
    
    if (variance < 0) { // Shortage
        const shortageQty = Math.abs(variance);
        
        // Procurement recommendation
        recommendations.push({
            type: 'PROCUREMENT_REQUEST',
            description: `Procure ${shortageQty} units of ${product.name} for ${plant.name}`,
            priority: 'HIGH',
            confidence: 0.7,
            quantity: shortageQty,
            estimatedCost: shortageQty * 50,
            estimatedTime: 7,
            reasoning: 'Direct procurement to address shortage',
            risks: 'Lead time and supplier availability',
            aiModel: 'FALLBACK_LOGIC'
        });
        
        // Production recommendation
        if (plant.capacity > 0) {
            recommendations.push({
                type: 'PRODUCTION_ORDER',
                description: `Produce ${shortageQty} units of ${product.name} at ${plant.name}`,
                priority: 'MEDIUM',
                confidence: 0.6,
                quantity: shortageQty,
                estimatedCost: shortageQty * 40,
                estimatedTime: 10,
                reasoning: 'Internal production to address shortage',
                risks: 'Production capacity and scheduling constraints',
                aiModel: 'FALLBACK_LOGIC'
            });
        }
    } else if (variance > 0) { // Surplus
        recommendations.push({
            type: 'STOCK_REALLOCATION',
            description: `Reallocate surplus of ${variance} units from ${plant.name} to other locations`,
            priority: 'LOW',
            confidence: 0.6,
            quantity: variance,
            estimatedCost: variance * 5,
            estimatedTime: 3,
            reasoning: 'Optimize inventory distribution',
            risks: 'Transportation and handling costs',
            aiModel: 'FALLBACK_LOGIC'
        });
    }
    
    return {
        success: true,
        recommendations,
        ai_model: 'FALLBACK_LOGIC',
        processing_time_ms: 100
    };
}

// Run the test
if (require.main === module) {
    testGenAIRecommendations().catch(console.error);
}

module.exports = { testGenAIRecommendations, generateFallbackRecommendations };

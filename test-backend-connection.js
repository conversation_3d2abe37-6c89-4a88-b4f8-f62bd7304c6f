// Test script to verify backend connection
console.log("Testing backend connection...");

// Test 1: Check if backend is accessible
fetch('http://localhost:4004/reconciliation')
  .then(response => response.json())
  .then(data => {
    console.log("✅ Backend is accessible");
    console.log("Available entities:", data.value.map(item => item.name));
    
    // Test 2: Check ReconciliationResults
    return fetch('http://localhost:4004/reconciliation/ReconciliationResults?$top=3');
  })
  .then(response => response.json())
  .then(data => {
    console.log("✅ ReconciliationResults loaded:", data.value.length, "items");
    console.log("Sample data:", data.value[0]);
    
    // Test 3: Check Plants
    return fetch('http://localhost:4004/reconciliation/Plants?$top=3');
  })
  .then(response => response.json())
  .then(data => {
    console.log("✅ Plants loaded:", data.value.length, "items");
    console.log("Sample plant:", data.value[0].plantName);
    
    // Test 4: Check Products
    return fetch('http://localhost:4004/reconciliation/Products?$top=3');
  })
  .then(response => response.json())
  .then(data => {
    console.log("✅ Products loaded:", data.value.length, "items");
    console.log("Sample product:", data.value[0].productName);
    
    console.log("🎉 All backend tests passed! Frontend should be able to connect.");
  })
  .catch(error => {
    console.error("❌ Backend connection failed:", error);
  });

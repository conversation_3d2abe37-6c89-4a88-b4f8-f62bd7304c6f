# Deployment Guide - Demand-Supply Reconciliation System

This guide provides step-by-step instructions for deploying the Demand-Supply Reconciliation System in different environments.

## 🚀 Quick Start (Local Development)

### Prerequisites
- SAP Business Application Studio (BAS) access
- SAP HANA Cloud instance
- Python 3.11+ (available in BAS)
- Node.js 18+ and npm (pre-installed in BAS)

### SAP Business Application Studio Setup

```bash
# 1. Open project in BAS
# Create new dev space with CAP template

# 2. Install dependencies
npm run bas:setup
cd flask-api && pip3 install -r requirements.txt && cd ..

# 3. Configure HANA Cloud connection
# Create default-env.json with HANA credentials

# 4. Deploy database schema
npm run bas:deploy

# 5. Seed sample data
npm run seed

# 6. Start development services
# Terminal 1: npm run bas:dev
# Terminal 2: cd flask-api && python3 app.py
```

📋 **For detailed BAS setup, see [BAS-SETUP.md](BAS-SETUP.md)**

## 🏢 Production Deployment

### SAP BTP Cloud Foundry

#### 1. Prepare for Deployment

```bash
# Install CF CLI
# Login to your SAP BTP account
cf login -a <api-endpoint>

# Create services
cf create-service hana hdi-shared reconciliation-db
cf create-service xsuaa application reconciliation-auth
```

#### 2. Configure manifest.yml

```yaml
applications:
- name: reconciliation-system
  memory: 1G
  disk_quota: 1G
  buildpacks:
    - nodejs_buildpack
  services:
    - reconciliation-db
    - reconciliation-auth
  env:
    NODE_ENV: production
    AI_SERVICE_URL: https://your-ai-service.cfapps.sap.hana.ondemand.com
```

#### 3. Deploy CAP Application

```bash
# Build and deploy
npm run build
cf push

# Deploy database artifacts
cf deploy mta_archives/reconciliation-system_1.0.0.mtar
```

#### 4. Deploy AI Service

```bash
# Navigate to AI service
cd flask-api

# Create manifest for AI service
cat > manifest.yml << EOF
applications:
- name: reconciliation-ai
  memory: 512M
  buildpack: python_buildpack
  env:
    FLASK_ENV: production
    USE_OLLAMA: false
EOF

# Deploy AI service
cf push reconciliation-ai
```

### SAP BTP Kyma Runtime (Optional)

For advanced deployments, you can use SAP BTP Kyma Runtime:

```bash
# 1. Connect to Kyma cluster
kubectl config use-context <kyma-context>

# 2. Create namespace
kubectl create namespace reconciliation-system

# 3. Deploy using Helm or kubectl
# Create deployment manifests for CAP and AI services

# 4. Configure ingress and services
# Set up proper routing and SSL certificates
```

📋 **For Kyma deployment details, contact your SAP BTP administrator**

## 🔧 Configuration

### Environment Variables

#### CAP Service
```bash
# Database
DB_KIND=hana-cloud
DB_HOST=your-hana-host
DB_PORT=443
DB_USER=your-username
DB_PASSWORD=your-password
DB_SCHEMA=your-schema

# AI Integration
AI_SERVICE_URL=http://localhost:5000
CONFIDENCE_THRESHOLD=0.8
AUTO_TRIGGER_ENABLED=true

# Application
NODE_ENV=production
PORT=4004
```

#### AI Service
```bash
# AI Model Configuration
USE_OLLAMA=true
OLLAMA_URL=http://localhost:11434
MODEL_NAME=mistral:7b

# Alternative: Hugging Face
USE_OLLAMA=false
HF_MODEL=mistralai/Mistral-7B-Instruct-v0.1
HF_TOKEN=your-huggingface-token

# Service Configuration
PORT=5000
DEBUG=false
```

### Database Configuration

#### HANA Cloud
```json
{
  "cds": {
    "requires": {
      "db": {
        "kind": "hana-cloud",
        "credentials": {
          "host": "your-hana-host.hanacloud.ondemand.com",
          "port": "443",
          "user": "your-username",
          "password": "your-password",
          "schema": "RECONCILIATION",
          "encrypt": true
        }
      }
    }
  }
}
```

#### HANA Express (Development)
```json
{
  "cds": {
    "requires": {
      "db": {
        "kind": "hana",
        "credentials": {
          "host": "localhost",
          "port": "39013",
          "user": "SYSTEM",
          "password": "HanaExpress123",
          "schema": "RECONCILIATION"
        }
      }
    }
  }
}
```

## 🧪 Testing Deployment

### Health Checks

```bash
# Test CAP service
curl http://localhost:4004/reconciliation/$metadata

# Test AI service
curl http://localhost:5000/health

# Test UI5 app
curl http://localhost:4004/app/reconciliation-dashboard/webapp/

# Run integration tests
npm run test-integration
```

### Load Testing

```bash
# Install artillery
npm install -g artillery

# Create load test config
cat > load-test.yml << EOF
config:
  target: 'http://localhost:4004'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Reconciliation API"
    requests:
      - post:
          url: "/reconciliation/runReconciliation"
          json:
            plantCode: "P001"
            productCode: "PROD001"
            reconciliationDate: "2024-01-15"
EOF

# Run load test
artillery run load-test.yml
```

## 🔒 Security Configuration

### Authentication (SAP BTP)

```json
{
  "cds": {
    "requires": {
      "auth": {
        "kind": "xsuaa",
        "credentials": {
          "xsappname": "reconciliation-system",
          "clientid": "your-client-id",
          "clientsecret": "your-client-secret",
          "url": "https://your-tenant.authentication.sap.hana.ondemand.com"
        }
      }
    }
  }
}
```

### HTTPS Configuration

```javascript
// For production, use HTTPS
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
};

https.createServer(options, app).listen(443);
```

## 📊 Monitoring

### Application Monitoring

```bash
# Install monitoring tools
npm install --save express-prometheus-middleware prom-client

# Add to your app
const promMid = require('express-prometheus-middleware');
app.use(promMid({
  metricsPath: '/metrics',
  collectDefaultMetrics: true
}));
```

### Log Aggregation

```yaml
# docker-compose.override.yml for logging
version: '3.8'
services:
  cap-service:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
  
  ai-service:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check HANA connectivity
   telnet your-hana-host 443
   
   # Verify credentials
   # Check firewall settings
   ```

2. **AI Service Not Responding**
   ```bash
   # Check AI service logs
   docker logs reconciliation-ai
   
   # Test Ollama connection
   curl http://localhost:11434/api/tags
   ```

3. **UI5 App Not Loading**
   ```bash
   # Check CAP service status
   curl http://localhost:4004/reconciliation/$metadata
   
   # Verify static file serving
   # Check browser console for errors
   ```

### Performance Optimization

```bash
# Enable compression
npm install compression
# Add to app: app.use(compression())

# Enable caching
npm install node-cache
# Implement caching for master data

# Database optimization
# Add indexes for frequently queried fields
# Implement connection pooling
```

## 📋 Maintenance

### Backup Procedures

```bash
# Database backup (HANA)
# Use HANA Studio or SQL commands
BACKUP DATA USING FILE ('backup_location')

# Application backup
# Backup configuration files
# Backup custom code changes
```

### Updates and Patches

```bash
# Update dependencies
npm audit fix
npm update

# Update AI models
ollama pull mistral:latest

# Deploy updates
npm run build
cf push # or kubectl apply
```

---

For additional support, refer to the main README.md or contact the development team.

{"VCAP_SERVICES": {"hana": [{"label": "hana", "provider": null, "plan": "hdi-shared", "name": "reconciliation-hana-db", "tags": ["hana", "database", "relational"], "instance_name": "reconciliation-hana-db", "binding_name": null, "credentials": {"host": "YOUR_HANA_HOST.hanacloud.ondemand.com", "port": "443", "driver": "com.sap.db.jdbc.Driver", "url": "*********************************************************************************************************************", "schema": "YOUR_SCHEMA", "hdi_user": "YOUR_HDI_USER", "hdi_password": "YOUR_HDI_PASSWORD", "user": "YOUR_USER", "password": "YOUR_PASSWORD", "certificate": "-----B<PERSON>IN CERTIFICATE-----\nYOUR_CERTIFICATE_CONTENT\n-----END CERTIFICATE-----"}}]}, "USER_PROVIDED": [{"label": "user-provided", "name": "reconciliation-config", "tags": [], "credentials": {"AI_SERVICE_URL": "http://localhost:5002", "OLLAMA_URL": "http://localhost:11434", "OLLAMA_MODEL": "llama3.2:3b", "USE_OLLAMA": "true", "USE_BAS_AI": "false", "CONFIDENCE_THRESHOLD": "0.8", "AUTO_TRIGGER_ENABLED": "true"}}]}
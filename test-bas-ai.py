#!/usr/bin/env python3

"""
Test BAS AI Generation
This script tests the AI generation functionality in BAS
"""

import sys
import os
import requests
import json
import time

# Add flask-api to path
sys.path.append('flask-api')

def test_bas_ai_locally():
    """Test BAS AI Generator directly"""
    print("🤖 Testing BAS AI Generator Locally")
    print("=" * 50)
    
    try:
        from app import BASAIGenerator
        
        # Create AI generator
        ai_generator = BASAIGenerator()
        
        # Test data - shortage scenario
        test_data_shortage = {
            'plant': {
                'code': 'P001',
                'name': 'Manufacturing Plant North',
                'capacity': 10000
            },
            'product': {
                'code': 'PROD001',
                'name': 'Product A'
            },
            'demand': 100,
            'supply': 75,
            'stock': 30,
            'variance': -25,  # Shortage
            'context': {
                'reconciliationDate': '2024-01-15',
                'urgency': 'HIGH'
            }
        }
        
        # Test data - surplus scenario
        test_data_surplus = {
            'plant': {
                'code': 'P002',
                'name': 'Manufacturing Plant South',
                'capacity': 8000
            },
            'product': {
                'code': 'PROD002',
                'name': 'Product B'
            },
            'demand': 80,
            'supply': 110,
            'stock': 45,
            'variance': 30,  # Surplus
            'context': {
                'reconciliationDate': '2024-01-16',
                'urgency': 'MEDIUM'
            }
        }
        
        print("\n📊 Test Case 1: Shortage Scenario")
        print("-" * 30)
        recommendations_shortage = ai_generator.generate_intelligent_recommendation(test_data_shortage)
        
        for i, rec in enumerate(recommendations_shortage, 1):
            print(f"\n{i}. {rec['type']}")
            print(f"   Description: {rec['description']}")
            print(f"   Priority: {rec['priority']}")
            print(f"   Confidence: {rec['confidence']:.1%}")
            print(f"   Cost: ${rec['estimatedCost']:,}")
            print(f"   Time: {rec['estimatedTime']} days")
            print(f"   AI Model: {rec['aiModel']}")
        
        print("\n📊 Test Case 2: Surplus Scenario")
        print("-" * 30)
        recommendations_surplus = ai_generator.generate_intelligent_recommendation(test_data_surplus)
        
        for i, rec in enumerate(recommendations_surplus, 1):
            print(f"\n{i}. {rec['type']}")
            print(f"   Description: {rec['description']}")
            print(f"   Priority: {rec['priority']}")
            print(f"   Confidence: {rec['confidence']:.1%}")
            print(f"   Cost: ${rec['estimatedCost']:,}")
            print(f"   Time: {rec['estimatedTime']} days")
            print(f"   AI Model: {rec['aiModel']}")
        
        print("\n✅ BAS AI Generator is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing BAS AI: {e}")
        return False

def test_flask_api():
    """Test Flask API with BAS AI"""
    print("\n🌐 Testing Flask API with BAS AI")
    print("=" * 50)
    
    # Test data
    test_payload = {
        'plant': {
            'code': 'P001',
            'name': 'Manufacturing Plant North',
            'capacity': 10000
        },
        'product': {
            'code': 'PROD001',
            'name': 'Product A'
        },
        'demand': 100,
        'supply': 75,
        'stock': 30,
        'variance': -25,
        'context': {
            'reconciliationDate': '2024-01-15',
            'urgency': 'HIGH'
        }
    }
    
    try:
        # Test health endpoint
        print("🔍 Testing health endpoint...")
        health_response = requests.get('http://localhost:5000/health', timeout=5)
        print(f"✅ Health Status: {health_response.json()}")
        
        # Test recommendation endpoint
        print("\n🧠 Testing recommendation endpoint...")
        rec_response = requests.post(
            'http://localhost:5000/recommend',
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if rec_response.status_code == 200:
            result = rec_response.json()
            print("✅ API Response received!")
            print(f"   Success: {result.get('success')}")
            print(f"   AI Model: {result.get('ai_model')}")
            print(f"   Processing Time: {result.get('processing_time_ms')}ms")
            print(f"   Recommendations: {len(result.get('recommendations', []))}")
            
            for i, rec in enumerate(result.get('recommendations', []), 1):
                print(f"\n   {i}. {rec.get('type')}")
                print(f"      Priority: {rec.get('priority')}")
                print(f"      Confidence: {rec.get('confidence', 0):.1%}")
                print(f"      Description: {rec.get('description', '')[:100]}...")
            
            return True
        else:
            print(f"❌ API Error: {rec_response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Flask API is not running")
        print("💡 Start it with: cd flask-api && python3 app.py")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 BAS AI Generation Test Suite")
    print("=" * 60)
    
    # Test 1: Local AI Generator
    local_test = test_bas_ai_locally()
    
    # Test 2: Flask API
    api_test = test_flask_api()
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    print(f"✅ Local BAS AI Generator: {'PASS' if local_test else 'FAIL'}")
    print(f"✅ Flask API Integration: {'PASS' if api_test else 'FAIL'}")
    
    if local_test and api_test:
        print("\n🎉 All tests passed! BAS AI is fully functional!")
    elif local_test:
        print("\n⚠️  BAS AI works locally. Start Flask API for full functionality.")
    else:
        print("\n❌ Issues detected. Check the error messages above.")

if __name__ == "__main__":
    main()

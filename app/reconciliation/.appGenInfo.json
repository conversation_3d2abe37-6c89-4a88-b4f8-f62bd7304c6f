{"generationParameters": {"generationDate": "Thu Jul 31 2025 09:37:11 GMT+0000 (Coordinated Universal Time)", "generatorPlatform": "SAP Business Application Studio", "serviceType": "Local CAP", "metadataFilename": "", "serviceUrl": "http://localhost:4004/reconciliation/", "appName": "reconciliation", "appTitle": "Demand and Supply Reconciliation", "appDescription": "An SAP Fiori application.", "appNamespace": "", "ui5Theme": "sap_horizon", "ui5Version": "1.138.1", "enableCodeAssist": false, "enableEslint": false, "enableTypeScript": false, "showMockDataInfo": false, "generatorVersion": "1.18.3", "template": "Basic V4", "generatorName": "SAP Fiori Application Generator", "entityRelatedConfig": [], "launchText": "To launch the generated app, start your CAP project:  and navigate to the following location in your browser:\n\nhttp://localhost:4004/reconciliation/webapp/index.html"}}
<!DOCTYPE HTML>
<html lang="en">
<!-- Copyright (c) 2015 SAP AG, All Rights Reserved -->
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{appTitle}}</title>

    <!-- Bootstrap the unified shell in sandbox mode for standalone usage.

         The renderer is specified in the global Unified Shell configuration object "sap-ushell-config".

         The fiori2 renderer will render the shell header allowing, for instance,
         testing of additional application setting buttons.

         The navigation target resolution service is configured in a way that the empty URL hash is
         resolved to our own application.

         This example uses relative path references for the SAPUI5 resources and test-resources;
         it might be necessary to adapt them depending on the target runtime platform.
         The sandbox platform is restricted to development or demo use cases and must NOT be used
         for productive scenarios.
    -->
    <script type="text/javascript">
        window["sap-ushell-config"] = {
            defaultRenderer: "fiori2",
            bootstrapPlugins: {
                "RuntimeAuthoringPlugin": {
                    component: "sap.ushell.plugins.rta",
                    config: {
                        validateAppVersion: false
                    }
                }
            },
            renderers: {
                fiori2: {
                    componentData: {
                        config: {
                            search: "hidden",
                            enableSearch: false
                        }
                    }
                }
            },
            applications: {
                "reconciliation-display": {
                    title: "Demand and Supply Reconciliation",
                    description: "An SAP Fiori application.",
                    additionalInformation: "SAPUI5.Component=reconciliation",
                    applicationType: "URL",
                    url: "../"
                }
            }
        };
    </script>

    <script src="https://sapui5.hana.ondemand.com/1.138.1/test-resources/sap/ushell/bootstrap/sandbox.js" id="sap-ushell-bootstrap"></script>
    <!-- Bootstrap the UI5 core library. 'data-sap-ui-frameOptions="allow"'' is a NON-SECURE setting for test environments -->
    <script id="sap-ui-bootstrap"
        src="https://sapui5.hana.ondemand.com/1.138.1/resources/sap-ui-core.js"
        data-sap-ui-libs="sap.m,sap.ui.core,sap.ushell"
        data-sap-ui-async="true"
        data-sap-ui-preload="async"
        data-sap-ui-theme="sap_horizon"
        data-sap-ui-compatVersion="edge"
        data-sap-ui-language="en"
        data-sap-ui-resourceroots='{"reconciliation": "../"}'
        data-sap-ui-frameOptions="allow"
        data-sap-ui-flexibilityServices='[{"connector": "LocalStorageConnector"}]'>
    </script>
    <script>
        sap.ui.getCore().attachInit(function () {
            sap.ushell.Container.createRenderer().placeAt("content");
        });
    </script>
</head>

<!-- UI Content -->

<body class="sapUiBody" id="content">
</body>

</html>

/* Custom CSS for Demand-Supply Reconciliation System */

/* Global Styles */
.reconciliationApp {
    font-family: "72", "72full", Arial, Helvetica, sans-serif;
}

/* Dashboard Enhancements */
.dashboardKPITile {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    cursor: pointer;
}

.dashboardKPITile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboardKPITile .sapMGT {
    border-radius: 8px;
    border: 1px solid #e5e5e5;
}

.dashboardQuickAction {
    transition: background-color 0.2s ease-in-out;
}

.dashboardQuickAction:hover {
    background-color: #f5f5f5;
}

/* Upload View Enhancements */
.uploadSection {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.uploadSection .sapMPanel {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.uploadMethodSelector .sapMSegB {
    background-color: white;
    border-radius: 6px;
}

.uploadResults {
    background-color: #f8f9fa;
    border-left: 4px solid #0070f2;
    padding: 1rem;
    margin-top: 1rem;
}

.uploadResults.success {
    border-left-color: #30914c;
    background-color: #f1f8f3;
}

.uploadResults.error {
    border-left-color: #bb0000;
    background-color: #fef7f7;
}

/* Reconciliation Analysis Enhancements */
.analysisParameters {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.analysisProgress {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.analysisProgress .sapMPI {
    height: 8px;
    border-radius: 4px;
}

.analysisResults {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.resultsSummaryTile {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1rem;
    margin: 0.5rem;
    text-align: center;
    transition: transform 0.2s ease-in-out;
}

.resultsSummaryTile:hover {
    transform: scale(1.05);
}

.resultsSummaryTile.shortage {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.resultsSummaryTile.surplus {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.resultsSummaryTile.balanced {
    background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
}

/* AI Recommendations Enhancements */
.aiRecommendationCard {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    border-left: 4px solid #0070f2;
    margin-bottom: 1rem;
}

.aiRecommendationCard:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.aiRecommendationCard.highConfidence {
    border-left-color: #30914c;
}

.aiRecommendationCard.mediumConfidence {
    border-left-color: #e78200;
}

.aiRecommendationCard.lowConfidence {
    border-left-color: #bb0000;
}

.confidenceIndicator {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}

.confidenceIndicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.aiInsightsPanel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
}

.aiInsightsPanel .sapMMessageStrip {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

/* Actions Tracking Enhancements */
.actionStatusIndicator {
    position: relative;
    display: inline-block;
}

.actionStatusIndicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transform: translateY(-50%);
}

.actionStatusIndicator.pending::before {
    background-color: #e78200;
    animation: pulse 2s infinite;
}

.actionStatusIndicator.inProgress::before {
    background-color: #0070f2;
    animation: pulse 2s infinite;
}

.actionStatusIndicator.completed::before {
    background-color: #30914c;
}

.actionStatusIndicator.failed::before {
    background-color: #bb0000;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.actionProgressBar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    overflow: hidden;
}

.actionProgressBar .sapMPIBar {
    background: linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%);
}

/* Master Data Management Enhancements */
.masterDataEntitySelector {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.masterDataTable {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.masterDataTable .sapMListTblHeaderCell {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-weight: 600;
    color: #333;
}

.masterDataTable .sapMListTblRow:hover {
    background-color: #f8f9fa;
}

.masterDataStatistics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
}

.masterDataStatistics .sapMText {
    color: white;
}

/* Form Enhancements */
.formSection {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.formSection .sapMLabel {
    font-weight: 600;
    color: #333;
}

.formSection .sapMInputBase {
    border-radius: 4px;
}

.formSection .sapMInputBase:focus {
    border-color: #0070f2;
    box-shadow: 0 0 0 2px rgba(0, 112, 242, 0.2);
}

/* Button Enhancements */
.sapMBtn {
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
}

.sapMBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sapMBtnEmphasized {
    background: linear-gradient(135deg, #0070f2 0%, #0040a0 100%);
    border: none;
}

.sapMBtnAccept {
    background: linear-gradient(135deg, #30914c 0%, #2d5016 100%);
    border: none;
}

.sapMBtnReject {
    background: linear-gradient(135deg, #bb0000 0%, #8b0000 100%);
    border: none;
}

/* Panel Enhancements */
.sapMPanel {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e5e5;
}

.sapMPanel .sapMPanelHdr {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    color: #333;
}

/* Message Strip Enhancements */
.sapMMessageStrip {
    border-radius: 6px;
    border-left-width: 4px;
}

.sapMMessageStripInformation {
    background-color: #e3f2fd;
    border-left-color: #0070f2;
}

.sapMMessageStripSuccess {
    background-color: #e8f5e8;
    border-left-color: #30914c;
}

.sapMMessageStripWarning {
    background-color: #fff8e1;
    border-left-color: #e78200;
}

.sapMMessageStripError {
    background-color: #ffebee;
    border-left-color: #bb0000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboardKPITile {
        margin-bottom: 1rem;
    }

    .formSection {
        padding: 1rem;
    }

    .analysisParameters {
        padding: 1rem;
    }

    .aiInsightsPanel {
        padding: 1rem;
    }

    .masterDataStatistics {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .sapMBtn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .uploadSection {
        padding: 0.5rem;
    }

    .formSection {
        padding: 0.5rem;
    }
}

/* Loading and Animation Enhancements */
.loadingSpinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fadeIn {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slideInRight {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Accessibility Enhancements */
.sapMBtn:focus,
.sapMInputBase:focus,
.sapMLink:focus {
    outline: 2px solid #0070f2;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .dashboardKPITile,
    .aiRecommendationCard,
    .formSection,
    .sapMPanel {
        border: 2px solid #000;
    }

    .resultsSummaryTile {
        border: 2px solid #fff;
    }
}
sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState",
    "sap/ui/export/Spreadsheet"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState, Spreadsheet) {
    "use strict";

    return Controller.extend("reconciliation.controller.MasterData", {
        onInit: function () {
            // Initialize master data model
            this._initializeMasterDataModel();
            
            // Load initial data (plants by default)
            this._loadEntityData("plants");
        },

        _initializeMasterDataModel: function () {
            var oMasterDataModel = new JSONModel({
                selectedEntity: "plants",
                searchQuery: "",
                filters: {
                    status: "",
                    category: "",
                    region: ""
                },
                hasSelectedItems: false,
                currentData: [],
                plantsData: [],
                productsData: [],
                vendorsData: [],
                tableTitle: "Plants",
                columnHeaders: {
                    code: "Plant Code",
                    name: "Plant Name",
                    location: "Location"
                },
                statistics: {
                    total: 0,
                    active: 0,
                    inactive: 0,
                    recentlyAdded: 0,
                    categories: {
                        rawMaterials: 0,
                        finishedGoods: 0,
                        semiFinished: 0,
                        consumables: 0
                    },
                    regions: {
                        northAmerica: 0,
                        europe: 0,
                        asiaPacific: 0,
                        latinAmerica: 0
                    }
                }
            });
            this.getView().setModel(oMasterDataModel, "masterDataModel");
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onEntitySelectionChange: function (oEvent) {
            var sSelectedEntity = oEvent.getParameter("key");
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            
            oMasterDataModel.setProperty("/selectedEntity", sSelectedEntity);
            this._updateUIForEntity(sSelectedEntity);
            this._loadEntityData(sSelectedEntity);
        },

        onSearch: function () {
            this._applyFilters();
        },

        onFilterChange: function () {
            this._applyFilters();
        },

        onClearFilters: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            oMasterDataModel.setProperty("/searchQuery", "");
            oMasterDataModel.setProperty("/filters", {
                status: "",
                category: "",
                region: ""
            });
            
            // Clear search field
            this.byId("searchField").setValue("");
            
            this._applyFilters();
        },

        onRefreshData: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            
            MessageToast.show("Refreshing " + sSelectedEntity + " data...");
            this._loadEntityData(sSelectedEntity);
        },

        onSelectionChange: function (oEvent) {
            var oTable = oEvent.getSource();
            var aSelectedItems = oTable.getSelectedItems();
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            
            var bHasSelectedItems = aSelectedItems.length > 0;
            oMasterDataModel.setProperty("/hasSelectedItems", bHasSelectedItems);
        },

        onCreateNew: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            
            this._showCreateDialog(sSelectedEntity);
        },

        onBulkActivate: function () {
            var oTable = this.byId("masterDataTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select items to activate");
                return;
            }
            
            MessageBox.confirm(
                "Activate " + aSelectedItems.length + " selected items?",
                {
                    title: "Bulk Activate",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkUpdateStatus(aSelectedItems, "ACTIVE");
                        }
                    }.bind(this)
                }
            );
        },

        onBulkDeactivate: function () {
            var oTable = this.byId("masterDataTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select items to deactivate");
                return;
            }
            
            MessageBox.confirm(
                "Deactivate " + aSelectedItems.length + " selected items?",
                {
                    title: "Bulk Deactivate",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkUpdateStatus(aSelectedItems, "INACTIVE");
                        }
                    }.bind(this)
                }
            );
        },

        onBulkDelete: function () {
            var oTable = this.byId("masterDataTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select items to delete");
                return;
            }
            
            MessageBox.confirm(
                "Delete " + aSelectedItems.length + " selected items? This action cannot be undone.",
                {
                    title: "Bulk Delete",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkDelete(aSelectedItems);
                        }
                    }.bind(this)
                }
            );
        },

        onImportData: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            
            this._showImportDialog(sSelectedEntity);
        },

        onExportData: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            var aCurrentData = oMasterDataModel.getProperty("/currentData");
            
            if (!aCurrentData || aCurrentData.length === 0) {
                MessageToast.show("No data to export");
                return;
            }

            this._exportData(sSelectedEntity, aCurrentData);
        },

        onItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("masterDataModel");
            var oItem = oBindingContext.getObject();
            
            this._showItemDetails(oItem);
        },

        onViewDetails: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("masterDataModel");
            var oItem = oBindingContext.getObject();
            
            this._showItemDetails(oItem);
        },

        onEdit: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("masterDataModel");
            var oItem = oBindingContext.getObject();
            
            this._showEditDialog(oItem);
        },

        onDuplicate: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("masterDataModel");
            var oItem = oBindingContext.getObject();
            
            this._duplicateItem(oItem);
        },

        onDelete: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("masterDataModel");
            var oItem = oBindingContext.getObject();
            
            MessageBox.confirm(
                "Delete this item? This action cannot be undone.",
                {
                    title: "Delete Item",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._deleteItem(oItem);
                        }
                    }.bind(this)
                }
            );
        },

        // Helper Methods
        _loadEntityData: function (sEntity) {
            var oModel = this.getView().getModel();
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sEntityPath = "";
            
            switch (sEntity) {
                case "plants":
                    sEntityPath = "/Plants";
                    break;
                case "products":
                    sEntityPath = "/Products";
                    break;
                case "vendors":
                    sEntityPath = "/Vendors";
                    break;
            }
            
            oModel.read(sEntityPath, {
                success: function (oData) {
                    var aData = oData.results || [];
                    oMasterDataModel.setProperty("/" + sEntity + "Data", aData);
                    oMasterDataModel.setProperty("/currentData", aData);
                    
                    // Calculate statistics
                    this._calculateStatistics(sEntity, aData);
                    
                    // Apply current filters
                    this._applyFilters();
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load " + sEntity + " data");
                    console.error(sEntity + " loading error:", oError);
                }.bind(this)
            });
        },

        _updateUIForEntity: function (sEntity) {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            
            switch (sEntity) {
                case "plants":
                    oMasterDataModel.setProperty("/tableTitle", "Plants");
                    oMasterDataModel.setProperty("/columnHeaders", {
                        code: "Plant Code",
                        name: "Plant Name",
                        location: "Location"
                    });
                    break;
                case "products":
                    oMasterDataModel.setProperty("/tableTitle", "Products");
                    oMasterDataModel.setProperty("/columnHeaders", {
                        code: "Product Code",
                        name: "Product Name",
                        location: "Category"
                    });
                    break;
                case "vendors":
                    oMasterDataModel.setProperty("/tableTitle", "Vendors");
                    oMasterDataModel.setProperty("/columnHeaders", {
                        code: "Vendor Code",
                        name: "Vendor Name",
                        location: "Contact Person"
                    });
                    break;
            }
        },

        _calculateStatistics: function (sEntity, aData) {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var oStats = {
                total: aData.length,
                active: 0,
                inactive: 0,
                recentlyAdded: 0,
                categories: {
                    rawMaterials: 0,
                    finishedGoods: 0,
                    semiFinished: 0,
                    consumables: 0
                },
                regions: {
                    northAmerica: 0,
                    europe: 0,
                    asiaPacific: 0,
                    latinAmerica: 0
                }
            };

            var oOneMonthAgo = new Date();
            oOneMonthAgo.setMonth(oOneMonthAgo.getMonth() - 1);

            aData.forEach(function(oItem) {
                // Status statistics
                if (oItem.status === "ACTIVE") {
                    oStats.active++;
                } else if (oItem.status === "INACTIVE") {
                    oStats.inactive++;
                }

                // Recently added
                if (oItem.createdAt && new Date(oItem.createdAt) > oOneMonthAgo) {
                    oStats.recentlyAdded++;
                }

                // Category statistics (for products)
                if (sEntity === "products" && oItem.category) {
                    switch (oItem.category) {
                        case "RAW_MATERIALS":
                            oStats.categories.rawMaterials++;
                            break;
                        case "FINISHED_GOODS":
                            oStats.categories.finishedGoods++;
                            break;
                        case "SEMI_FINISHED":
                            oStats.categories.semiFinished++;
                            break;
                        case "CONSUMABLES":
                            oStats.categories.consumables++;
                            break;
                    }
                }

                // Regional statistics (for plants and vendors)
                if ((sEntity === "plants" || sEntity === "vendors") && oItem.region) {
                    switch (oItem.region) {
                        case "NORTH_AMERICA":
                            oStats.regions.northAmerica++;
                            break;
                        case "EUROPE":
                            oStats.regions.europe++;
                            break;
                        case "ASIA_PACIFIC":
                            oStats.regions.asiaPacific++;
                            break;
                        case "LATIN_AMERICA":
                            oStats.regions.latinAmerica++;
                            break;
                    }
                }
            });

            oMasterDataModel.setProperty("/statistics", oStats);
        },

        _applyFilters: function () {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            var sSearchQuery = oMasterDataModel.getProperty("/searchQuery").toLowerCase();
            var oFilters = oMasterDataModel.getProperty("/filters");
            var aAllData = oMasterDataModel.getProperty("/" + sSelectedEntity + "Data");

            if (!aAllData) return;

            var aFilteredData = aAllData.filter(function(oItem) {
                // Search filter
                if (sSearchQuery) {
                    var sName = (oItem.plantName || oItem.productName || oItem.vendorName || "").toLowerCase();
                    var sCode = (oItem.plantCode || oItem.productCode || oItem.vendorCode || "").toLowerCase();
                    var sDescription = (oItem.description || "").toLowerCase();

                    if (sName.indexOf(sSearchQuery) === -1 &&
                        sCode.indexOf(sSearchQuery) === -1 &&
                        sDescription.indexOf(sSearchQuery) === -1) {
                        return false;
                    }
                }

                // Status filter
                if (oFilters.status && oItem.status !== oFilters.status) {
                    return false;
                }

                // Category filter (for products)
                if (oFilters.category && oItem.category !== oFilters.category) {
                    return false;
                }

                // Region filter (for plants and vendors)
                if (oFilters.region && oItem.region !== oFilters.region) {
                    return false;
                }

                return true;
            });

            oMasterDataModel.setProperty("/currentData", aFilteredData);
        },

        _showCreateDialog: function (sEntity) {
            var sTitle = "Create New " + sEntity.charAt(0).toUpperCase() + sEntity.slice(1, -1);
            var sMessage = "Create a new " + sEntity.slice(0, -1) + "?";

            MessageBox.confirm(sMessage, {
                title: sTitle,
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._createNewItem(sEntity);
                    }
                }.bind(this)
            });
        },

        _showImportDialog: function (sEntity) {
            var sTitle = "Import " + sEntity.charAt(0).toUpperCase() + sEntity.slice(1) + " Data";
            var sMessage = "Import " + sEntity + " data from file?";

            MessageBox.confirm(sMessage, {
                title: sTitle,
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._importData(sEntity);
                    }
                }.bind(this)
            });
        },

        _showItemDetails: function (oItem) {
            var oMasterDataModel = this.getView().getModel("masterDataModel");
            var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
            var sDetails = "";

            switch (sSelectedEntity) {
                case "plants":
                    sDetails = "Plant Details:\n\n" +
                        "Plant Code: " + oItem.plantCode + "\n" +
                        "Plant Name: " + oItem.plantName + "\n" +
                        "Description: " + (oItem.description || "") + "\n" +
                        "Location: " + (oItem.location || "") + "\n" +
                        "Region: " + (oItem.region || "") + "\n" +
                        "Status: " + oItem.status + "\n" +
                        "Created: " + this.formatDate(oItem.createdAt);
                    break;
                case "products":
                    sDetails = "Product Details:\n\n" +
                        "Product Code: " + oItem.productCode + "\n" +
                        "Product Name: " + oItem.productName + "\n" +
                        "Description: " + (oItem.description || "") + "\n" +
                        "Category: " + (oItem.category || "") + "\n" +
                        "Unit of Measure: " + (oItem.unitOfMeasure || "") + "\n" +
                        "Status: " + oItem.status + "\n" +
                        "Created: " + this.formatDate(oItem.createdAt);
                    break;
                case "vendors":
                    sDetails = "Vendor Details:\n\n" +
                        "Vendor Code: " + oItem.vendorCode + "\n" +
                        "Vendor Name: " + oItem.vendorName + "\n" +
                        "Description: " + (oItem.description || "") + "\n" +
                        "Contact Person: " + (oItem.contactPerson || "") + "\n" +
                        "Email: " + (oItem.email || "") + "\n" +
                        "Phone: " + (oItem.phone || "") + "\n" +
                        "Region: " + (oItem.region || "") + "\n" +
                        "Status: " + oItem.status + "\n" +
                        "Created: " + this.formatDate(oItem.createdAt);
                    break;
            }

            MessageBox.information(sDetails, {
                title: "Item Details"
            });
        },

        _showEditDialog: function (oItem) {
            var sMessage = "Edit this item?";

            MessageBox.confirm(sMessage, {
                title: "Edit Item",
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._editItem(oItem);
                    }
                }.bind(this)
            });
        },

        _exportData: function (sEntity, aData) {
            var aExportData = [];
            var aColumns = [];

            switch (sEntity) {
                case "plants":
                    aColumns = [
                        { label: "Plant Code", property: "PlantCode" },
                        { label: "Plant Name", property: "PlantName" },
                        { label: "Description", property: "Description" },
                        { label: "Location", property: "Location" },
                        { label: "Region", property: "Region" },
                        { label: "Status", property: "Status" },
                        { label: "Created At", property: "CreatedAt" }
                    ];
                    aExportData = aData.map(function(oItem) {
                        return {
                            PlantCode: oItem.plantCode,
                            PlantName: oItem.plantName,
                            Description: oItem.description || "",
                            Location: oItem.location || "",
                            Region: oItem.region || "",
                            Status: oItem.status,
                            CreatedAt: oItem.createdAt
                        };
                    });
                    break;
                case "products":
                    aColumns = [
                        { label: "Product Code", property: "ProductCode" },
                        { label: "Product Name", property: "ProductName" },
                        { label: "Description", property: "Description" },
                        { label: "Category", property: "Category" },
                        { label: "Unit of Measure", property: "UnitOfMeasure" },
                        { label: "Status", property: "Status" },
                        { label: "Created At", property: "CreatedAt" }
                    ];
                    aExportData = aData.map(function(oItem) {
                        return {
                            ProductCode: oItem.productCode,
                            ProductName: oItem.productName,
                            Description: oItem.description || "",
                            Category: oItem.category || "",
                            UnitOfMeasure: oItem.unitOfMeasure || "",
                            Status: oItem.status,
                            CreatedAt: oItem.createdAt
                        };
                    });
                    break;
                case "vendors":
                    aColumns = [
                        { label: "Vendor Code", property: "VendorCode" },
                        { label: "Vendor Name", property: "VendorName" },
                        { label: "Description", property: "Description" },
                        { label: "Contact Person", property: "ContactPerson" },
                        { label: "Email", property: "Email" },
                        { label: "Phone", property: "Phone" },
                        { label: "Region", property: "Region" },
                        { label: "Status", property: "Status" },
                        { label: "Created At", property: "CreatedAt" }
                    ];
                    aExportData = aData.map(function(oItem) {
                        return {
                            VendorCode: oItem.vendorCode,
                            VendorName: oItem.vendorName,
                            Description: oItem.description || "",
                            ContactPerson: oItem.contactPerson || "",
                            Email: oItem.email || "",
                            Phone: oItem.phone || "",
                            Region: oItem.region || "",
                            Status: oItem.status,
                            CreatedAt: oItem.createdAt
                        };
                    });
                    break;
            }

            // Create and download spreadsheet
            var oSpreadsheet = new Spreadsheet({
                workbook: {
                    columns: aColumns
                },
                dataSource: aExportData,
                fileName: sEntity.charAt(0).toUpperCase() + sEntity.slice(1) + "_" + new Date().toISOString().split('T')[0] + ".xlsx"
            });

            oSpreadsheet.build()
                .then(function() {
                    MessageToast.show(sEntity.charAt(0).toUpperCase() + sEntity.slice(1) + " data exported successfully");
                })
                .catch(function(oError) {
                    MessageBox.error("Failed to export data: " + oError.message);
                });
        },

        _bulkUpdateStatus: function (aSelectedItems, sNewStatus) {
            MessageToast.show("Updating status for " + aSelectedItems.length + " items...");

            // Simulate bulk update
            setTimeout(function() {
                MessageToast.show("Bulk status update completed");
                var oMasterDataModel = this.getView().getModel("masterDataModel");
                var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
                this._loadEntityData(sSelectedEntity);
            }.bind(this), 2000);
        },

        _bulkDelete: function (aSelectedItems) {
            MessageToast.show("Deleting " + aSelectedItems.length + " items...");

            // Simulate bulk delete
            setTimeout(function() {
                MessageToast.show("Bulk delete completed");
                var oMasterDataModel = this.getView().getModel("masterDataModel");
                var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
                this._loadEntityData(sSelectedEntity);
            }.bind(this), 2000);
        },

        _createNewItem: function (sEntity) {
            MessageToast.show("Creating new " + sEntity.slice(0, -1) + "...");

            // Simulate item creation
            setTimeout(function() {
                MessageToast.show("Item created successfully");
                this._loadEntityData(sEntity);
            }.bind(this), 2000);
        },

        _editItem: function (oItem) {
            MessageToast.show("Editing item...");

            // Simulate item edit
            setTimeout(function() {
                MessageToast.show("Item updated successfully");
                var oMasterDataModel = this.getView().getModel("masterDataModel");
                var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
                this._loadEntityData(sSelectedEntity);
            }.bind(this), 2000);
        },

        _duplicateItem: function (oItem) {
            MessageToast.show("Duplicating item...");

            // Simulate item duplication
            setTimeout(function() {
                MessageToast.show("Item duplicated successfully");
                var oMasterDataModel = this.getView().getModel("masterDataModel");
                var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
                this._loadEntityData(sSelectedEntity);
            }.bind(this), 2000);
        },

        _deleteItem: function (oItem) {
            MessageToast.show("Deleting item...");

            // Simulate item deletion
            setTimeout(function() {
                MessageToast.show("Item deleted successfully");
                var oMasterDataModel = this.getView().getModel("masterDataModel");
                var sSelectedEntity = oMasterDataModel.getProperty("/selectedEntity");
                this._loadEntityData(sSelectedEntity);
            }.bind(this), 2000);
        },

        _importData: function (sEntity) {
            MessageToast.show("Importing " + sEntity + " data...");

            // Simulate data import
            setTimeout(function() {
                MessageToast.show("Data imported successfully");
                this._loadEntityData(sEntity);
            }.bind(this), 3000);
        },

        // Formatters
        formatStatusState: function (sStatus) {
            switch (sStatus) {
                case "ACTIVE": return ValueState.Success;
                case "INACTIVE": return ValueState.Error;
                default: return ValueState.None;
            }
        },

        formatDate: function (sDate) {
            if (!sDate) return "";
            var oDate = new Date(sDate);
            return oDate.toLocaleDateString() + " " + oDate.toLocaleTimeString();
        }
    });
});

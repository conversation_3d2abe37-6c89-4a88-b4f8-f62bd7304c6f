sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState",
    "sap/ui/export/Spreadsheet"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState, Spreadsheet) {
    "use strict";

    return Controller.extend("reconciliation.controller.Analytics", {
        onInit: function () {
            // Initialize analytics model
            this._initializeAnalyticsModel();
            
            // Load analytics data
            this._loadAnalyticsData();
            
            // Initialize charts
            this._initializeCharts();
        },

        _initializeAnalyticsModel: function () {
            var oAnalyticsModel = new JSONModel({
                selectedPeriod: "lastMonth",
                customDateRange: "",
                kpis: {
                    reconciliationAccuracy: {
                        value: "94.2%",
                        state: "Good",
                        trend: "Up"
                    },
                    avgResolutionTime: {
                        value: "4.8",
                        state: "Good",
                        trend: "Down"
                    },
                    aiRecommendationSuccess: {
                        value: "87.5%",
                        state: "Good",
                        trend: "Up"
                    },
                    costSavings: {
                        value: "$125,430",
                        state: "Good",
                        trend: "Up"
                    }
                },
                chartData: {
                    reconciliationTrends: [],
                    plantPerformance: [],
                    aiPerformance: []
                },
                detailedAnalytics: [],
                insights: {
                    summary: "Overall system performance is excellent with 94.2% accuracy rate and significant cost savings achieved.",
                    keyInsights: [
                        { text: "Plant A shows the highest reconciliation accuracy at 97.8%" },
                        { text: "AI recommendations have improved resolution time by 35%" },
                        { text: "Stock reallocation actions are most effective for cost savings" },
                        { text: "Peak reconciliation activity occurs during month-end periods" }
                    ],
                    recommendations: [
                        { text: "Implement AI recommendations more broadly across all plants" },
                        { text: "Focus on improving Plant C performance through targeted training" },
                        { text: "Consider automated stock reallocation for high-frequency scenarios" },
                        { text: "Optimize reconciliation scheduling to balance workload" }
                    ]
                }
            });
            this.getView().setModel(oAnalyticsModel, "analyticsModel");
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onPeriodChange: function (oEvent) {
            var sSelectedPeriod = oEvent.getParameter("key");
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            
            oAnalyticsModel.setProperty("/selectedPeriod", sSelectedPeriod);
            this._loadAnalyticsData();
        },

        onCustomDateChange: function () {
            this._loadAnalyticsData();
        },

        onRefreshAnalytics: function () {
            MessageToast.show("Refreshing analytics data...");
            this._loadAnalyticsData();
        },

        onKPIPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("analyticsModel");
            var sKPIPath = oBindingContext.getPath();
            var sKPIName = sKPIPath.split("/").pop();
            
            MessageBox.information("Detailed " + sKPIName + " analytics would be displayed here.", {
                title: "KPI Details"
            });
        },

        onAnalyticsItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("analyticsModel");
            var oPlantData = oBindingContext.getObject();
            
            this._showPlantAnalyticsDetails(oPlantData);
        },

        onPlantLinkPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("analyticsModel");
            var oPlantData = oBindingContext.getObject();
            
            this._showPlantAnalyticsDetails(oPlantData);
        },

        onExportAnalytics: function () {
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            var aAnalyticsData = oAnalyticsModel.getProperty("/detailedAnalytics");
            
            if (!aAnalyticsData || aAnalyticsData.length === 0) {
                MessageToast.show("No analytics data to export");
                return;
            }

            this._exportAnalyticsData(aAnalyticsData);
        },

        // Helper Methods
        _loadAnalyticsData: function () {
            var oModel = this.getView().getModel();
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            var sSelectedPeriod = oAnalyticsModel.getProperty("/selectedPeriod");
            
            // Load KPI data
            this._loadKPIData(sSelectedPeriod);
            
            // Load chart data
            this._loadChartData(sSelectedPeriod);
            
            // Load detailed analytics
            this._loadDetailedAnalytics(sSelectedPeriod);
            
            // Generate insights
            this._generateInsights();
        },

        _loadKPIData: function (sPeriod) {
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            
            // Simulate KPI data loading based on period
            var oKPIs = {
                reconciliationAccuracy: {
                    value: "94.2%",
                    state: "Good",
                    trend: "Up"
                },
                avgResolutionTime: {
                    value: "4.8",
                    state: "Good",
                    trend: "Down"
                },
                aiRecommendationSuccess: {
                    value: "87.5%",
                    state: "Good",
                    trend: "Up"
                },
                costSavings: {
                    value: "$125,430",
                    state: "Good",
                    trend: "Up"
                }
            };
            
            // Adjust values based on period
            switch (sPeriod) {
                case "lastWeek":
                    oKPIs.costSavings.value = "$28,750";
                    break;
                case "lastQuarter":
                    oKPIs.costSavings.value = "$387,250";
                    break;
                case "lastYear":
                    oKPIs.costSavings.value = "$1,542,180";
                    break;
            }
            
            oAnalyticsModel.setProperty("/kpis", oKPIs);
        },

        _loadChartData: function (sPeriod) {
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            
            // Generate sample chart data
            var oChartData = {
                reconciliationTrends: this._generateTrendData(sPeriod),
                plantPerformance: this._generatePlantPerformanceData(),
                aiPerformance: this._generateAIPerformanceData()
            };
            
            oAnalyticsModel.setProperty("/chartData", oChartData);
            
            // Update charts
            this._updateCharts();
        },

        _loadDetailedAnalytics: function (sPeriod) {
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            
            // Generate sample detailed analytics data
            var aDetailedAnalytics = [
                {
                    plantName: "Plant A - North America",
                    totalReconciliations: 1247,
                    accuracyRate: 97.8,
                    avgResolutionTime: 3.2,
                    aiRecommendations: 342,
                    costSavings: 45230,
                    lastUpdated: new Date().toISOString()
                },
                {
                    plantName: "Plant B - Europe",
                    totalReconciliations: 1089,
                    accuracyRate: 94.5,
                    avgResolutionTime: 4.1,
                    aiRecommendations: 298,
                    costSavings: 38750,
                    lastUpdated: new Date().toISOString()
                },
                {
                    plantName: "Plant C - Asia Pacific",
                    totalReconciliations: 892,
                    accuracyRate: 89.3,
                    avgResolutionTime: 6.8,
                    aiRecommendations: 245,
                    costSavings: 28940,
                    lastUpdated: new Date().toISOString()
                },
                {
                    plantName: "Plant D - Latin America",
                    totalReconciliations: 756,
                    accuracyRate: 92.1,
                    avgResolutionTime: 5.2,
                    aiRecommendations: 187,
                    costSavings: 22510,
                    lastUpdated: new Date().toISOString()
                }
            ];
            
            oAnalyticsModel.setProperty("/detailedAnalytics", aDetailedAnalytics);
        },

        _generateTrendData: function (sPeriod) {
            var aTrendData = [];
            var iDays = 30; // Default to 30 days
            
            switch (sPeriod) {
                case "lastWeek":
                    iDays = 7;
                    break;
                case "lastQuarter":
                    iDays = 90;
                    break;
                case "lastYear":
                    iDays = 365;
                    break;
            }
            
            for (var i = iDays; i >= 0; i--) {
                var oDate = new Date();
                oDate.setDate(oDate.getDate() - i);
                
                aTrendData.push({
                    date: oDate.toISOString().split('T')[0],
                    shortages: Math.floor(Math.random() * 50) + 10,
                    surpluses: Math.floor(Math.random() * 40) + 5,
                    balanced: Math.floor(Math.random() * 80) + 20
                });
            }
            
            return aTrendData;
        },

        _generatePlantPerformanceData: function () {
            return [
                { plantName: "Plant A", accuracyRate: 97.8, avgResolutionTime: 3.2 },
                { plantName: "Plant B", accuracyRate: 94.5, avgResolutionTime: 4.1 },
                { plantName: "Plant C", accuracyRate: 89.3, avgResolutionTime: 6.8 },
                { plantName: "Plant D", accuracyRate: 92.1, avgResolutionTime: 5.2 }
            ];
        },

        _generateAIPerformanceData: function () {
            return [
                { status: "Approved", count: 342 },
                { status: "Rejected", count: 45 },
                { status: "Pending", count: 23 },
                { status: "Executed", count: 298 }
            ];
        },

        _initializeCharts: function () {
            // Initialize chart configurations
            setTimeout(function() {
                this._configureCharts();
            }.bind(this), 100);
        },

        _configureCharts: function () {
            // Configure reconciliation trends chart
            var oReconciliationChart = this.byId("reconciliationTrendsChart");
            if (oReconciliationChart) {
                oReconciliationChart.setVizProperties({
                    title: { text: "Reconciliation Trends" },
                    legend: { visible: true },
                    plotArea: {
                        dataLabel: { visible: false }
                    }
                });
            }
            
            // Configure plant performance chart
            var oPlantChart = this.byId("plantPerformanceChart");
            if (oPlantChart) {
                oPlantChart.setVizProperties({
                    title: { text: "Plant Performance" },
                    legend: { visible: true },
                    plotArea: {
                        dataLabel: { visible: true }
                    }
                });
            }
            
            // Configure AI performance chart
            var oAIChart = this.byId("aiPerformanceChart");
            if (oAIChart) {
                oAIChart.setVizProperties({
                    title: { text: "AI Recommendations Status" },
                    legend: { visible: true },
                    plotArea: {
                        dataLabel: { visible: true }
                    }
                });
            }
        },

        _updateCharts: function () {
            // Trigger chart updates
            setTimeout(function() {
                var oReconciliationChart = this.byId("reconciliationTrendsChart");
                var oPlantChart = this.byId("plantPerformanceChart");
                var oAIChart = this.byId("aiPerformanceChart");
                
                if (oReconciliationChart) {
                    oReconciliationChart.invalidate();
                }
                if (oPlantChart) {
                    oPlantChart.invalidate();
                }
                if (oAIChart) {
                    oAIChart.invalidate();
                }
            }.bind(this), 100);
        },

        _generateInsights: function () {
            var oAnalyticsModel = this.getView().getModel("analyticsModel");
            
            // Generate dynamic insights based on current data
            var oInsights = {
                summary: "System performance analysis shows strong results with opportunities for optimization.",
                keyInsights: [
                    { text: "Reconciliation accuracy has improved by 12% over the selected period" },
                    { text: "AI-driven recommendations show 87.5% success rate" },
                    { text: "Average resolution time decreased by 25% with automation" },
                    { text: "Cost savings exceeded target by 18% this period" }
                ],
                recommendations: [
                    { text: "Expand AI recommendation usage to underperforming plants" },
                    { text: "Implement predictive analytics for demand forecasting" },
                    { text: "Optimize inventory levels based on reconciliation patterns" },
                    { text: "Consider real-time monitoring for critical supply chains" }
                ]
            };
            
            oAnalyticsModel.setProperty("/insights", oInsights);
        },

        _showPlantAnalyticsDetails: function (oPlantData) {
            var sDetails = "Plant Analytics Details:\n\n" +
                "Plant: " + oPlantData.plantName + "\n" +
                "Total Reconciliations: " + oPlantData.totalReconciliations + "\n" +
                "Accuracy Rate: " + this.formatPercentage(oPlantData.accuracyRate) + "\n" +
                "Average Resolution Time: " + this.formatDuration(oPlantData.avgResolutionTime) + "\n" +
                "AI Recommendations: " + oPlantData.aiRecommendations + "\n" +
                "Cost Savings: " + this.formatCurrency(oPlantData.costSavings) + "\n" +
                "Last Updated: " + this.formatDate(oPlantData.lastUpdated);

            MessageBox.information(sDetails, {
                title: "Plant Analytics Details"
            });
        },

        _exportAnalyticsData: function (aAnalyticsData) {
            var aColumns = [
                { label: "Plant Name", property: "PlantName" },
                { label: "Total Reconciliations", property: "TotalReconciliations" },
                { label: "Accuracy Rate", property: "AccuracyRate" },
                { label: "Avg Resolution Time", property: "AvgResolutionTime" },
                { label: "AI Recommendations", property: "AIRecommendations" },
                { label: "Cost Savings", property: "CostSavings" },
                { label: "Last Updated", property: "LastUpdated" }
            ];

            var aExportData = aAnalyticsData.map(function(oItem) {
                return {
                    PlantName: oItem.plantName,
                    TotalReconciliations: oItem.totalReconciliations,
                    AccuracyRate: oItem.accuracyRate + "%",
                    AvgResolutionTime: oItem.avgResolutionTime + " hours",
                    AIRecommendations: oItem.aiRecommendations,
                    CostSavings: "$" + oItem.costSavings.toLocaleString(),
                    LastUpdated: oItem.lastUpdated
                };
            }.bind(this));

            // Create and download spreadsheet
            var oSpreadsheet = new Spreadsheet({
                workbook: {
                    columns: aColumns
                },
                dataSource: aExportData,
                fileName: "Analytics_Report_" + new Date().toISOString().split('T')[0] + ".xlsx"
            });

            oSpreadsheet.build()
                .then(function() {
                    MessageToast.show("Analytics data exported successfully");
                })
                .catch(function(oError) {
                    MessageBox.error("Failed to export analytics data: " + oError.message);
                });
        },

        // Formatters
        formatPercentage: function (fValue) {
            return Math.round(fValue || 0) + "%";
        },

        formatAccuracyState: function (fAccuracy) {
            if (fAccuracy >= 95) return ValueState.Success;
            if (fAccuracy >= 90) return ValueState.Warning;
            return ValueState.Error;
        },

        formatDuration: function (fHours) {
            if (!fHours) return "0h";
            if (fHours < 1) {
                return Math.round(fHours * 60) + "m";
            }
            return fHours.toFixed(1) + "h";
        },

        formatCurrency: function (fAmount) {
            if (!fAmount) return "$0";
            return "$" + fAmount.toLocaleString();
        },

        formatDate: function (sDate) {
            if (!sDate) return "";
            var oDate = new Date(sDate);
            return oDate.toLocaleDateString() + " " + oDate.toLocaleTimeString();
        }
    });
});

sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState",
    "sap/ui/export/Spreadsheet"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState, Spreadsheet) {
    "use strict";

    return Controller.extend("reconciliation.controller.Actions", {
        onInit: function () {
            // Initialize actions model
            this._initializeActionsModel();
            
            // Load actions data
            this._loadActions();
            
            // Set up auto-refresh
            this._setupAutoRefresh();
        },

        _initializeActionsModel: function () {
            var oActionsModel = new JSONModel({
                actions: [],
                kpis: {
                    totalActions: 0,
                    pendingActions: 0,
                    inProgressActions: 0,
                    completedActions: 0,
                    failedActions: 0,
                    successRate: 0,
                    avgExecutionTime: 0
                },
                filters: {
                    actionType: "",
                    status: "",
                    priority: "",
                    dateRange: null
                },
                hasSelectedActions: false
            });
            this.getView().setModel(oActionsModel, "actionsModel");
        },

        _loadActions: function () {
            var oModel = this.getView().getModel();
            var oActionsModel = this.getView().getModel("actionsModel");
            
            // Load reconciliation actions
            oModel.read("/ReconciliationActions", {
                urlParameters: {
                    "$expand": "plant,product,recommendation",
                    "$orderby": "createdAt desc"
                },
                success: function (oData) {
                    var aActions = oData.results || [];
                    oActionsModel.setProperty("/actions", aActions);
                    
                    // Calculate KPIs
                    this._calculateKPIs(aActions);
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load actions data");
                    console.error("Actions loading error:", oError);
                }.bind(this)
            });
        },

        _setupAutoRefresh: function () {
            // Auto-refresh every 30 seconds
            this._refreshInterval = setInterval(function() {
                this._loadActions();
            }.bind(this), 30000);
        },

        // Event Handlers
        onNavBack: function () {
            // Clear auto-refresh interval
            if (this._refreshInterval) {
                clearInterval(this._refreshInterval);
            }
            
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onKPITilePress: function (oEvent) {
            var sTileId = oEvent.getSource().getId();
            var sFilter = "";
            
            if (sTileId.includes("pending")) {
                sFilter = "PENDING";
            } else if (sTileId.includes("inProgress")) {
                sFilter = "IN_PROGRESS";
            } else if (sTileId.includes("completed")) {
                sFilter = "COMPLETED";
            }
            
            if (sFilter) {
                var oActionsModel = this.getView().getModel("actionsModel");
                oActionsModel.setProperty("/filters/status", sFilter);
                this._applyFilters();
            }
        },

        onFilterChange: function () {
            this._applyFilters();
        },

        onDateRangeChange: function () {
            this._applyFilters();
        },

        onClearFilters: function () {
            var oActionsModel = this.getView().getModel("actionsModel");
            oActionsModel.setProperty("/filters", {
                actionType: "",
                status: "",
                priority: "",
                dateRange: null
            });
            
            // Clear date range picker
            this.byId("dateRangeFilter").setValue("");
            
            this._applyFilters();
        },

        onRefreshActions: function () {
            MessageToast.show("Refreshing actions...");
            this._loadActions();
        },

        onSelectionChange: function (oEvent) {
            var oTable = oEvent.getSource();
            var aSelectedItems = oTable.getSelectedItems();
            var oActionsModel = this.getView().getModel("actionsModel");
            
            var bHasSelectedActions = aSelectedItems.length > 0;
            oActionsModel.setProperty("/hasSelectedActions", bHasSelectedActions);
        },

        onCreateStockReallocation: function () {
            this._showCreateActionDialog("STOCK_REALLOCATION");
        },

        onCreateProcurementRequest: function () {
            this._showCreateActionDialog("PROCUREMENT_REQUEST");
        },

        onCreateProductionOrder: function () {
            this._showCreateActionDialog("PRODUCTION_ORDER");
        },

        onBulkUpdateStatus: function () {
            var oTable = this.byId("actionsTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select actions to update");
                return;
            }
            
            this._showBulkUpdateDialog(aSelectedItems);
        },

        onActionItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("actionsModel");
            var oAction = oBindingContext.getObject();
            
            this._showActionDetails(oAction);
        },

        onViewActionDetails: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("actionsModel");
            var oAction = oBindingContext.getObject();
            
            this._showActionDetails(oAction);
        },

        onUpdateActionStatus: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("actionsModel");
            var oAction = oBindingContext.getObject();
            
            this._showUpdateStatusDialog(oAction);
        },

        onCancelAction: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("actionsModel");
            var oAction = oBindingContext.getObject();
            
            MessageBox.confirm(
                "Cancel action '" + oAction.actionId + "'?",
                {
                    title: "Cancel Action",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._cancelAction(oAction);
                        }
                    }.bind(this)
                }
            );
        },

        onExportActions: function () {
            var oActionsModel = this.getView().getModel("actionsModel");
            var aActions = oActionsModel.getProperty("/actions");
            
            if (!aActions || aActions.length === 0) {
                MessageToast.show("No actions to export");
                return;
            }

            // Prepare data for export
            var aExportData = aActions.map(function(oAction) {
                return {
                    ActionID: oAction.actionId,
                    Type: oAction.actionType,
                    Plant: oAction.plant.plantName,
                    Product: oAction.product.productName,
                    Description: oAction.description,
                    Priority: oAction.priority,
                    Status: oAction.status,
                    Progress: oAction.progress + "%",
                    CreatedAt: oAction.createdAt,
                    UpdatedAt: oAction.updatedAt,
                    EstimatedCompletion: oAction.estimatedCompletion
                };
            });

            // Create and download spreadsheet
            var oSpreadsheet = new Spreadsheet({
                workbook: {
                    columns: [
                        { label: "Action ID", property: "ActionID" },
                        { label: "Type", property: "Type" },
                        { label: "Plant", property: "Plant" },
                        { label: "Product", property: "Product" },
                        { label: "Description", property: "Description" },
                        { label: "Priority", property: "Priority" },
                        { label: "Status", property: "Status" },
                        { label: "Progress", property: "Progress" },
                        { label: "Created At", property: "CreatedAt" },
                        { label: "Updated At", property: "UpdatedAt" },
                        { label: "Estimated Completion", property: "EstimatedCompletion" }
                    ]
                },
                dataSource: aExportData,
                fileName: "ReconciliationActions_" + new Date().toISOString().split('T')[0] + ".xlsx"
            });

            oSpreadsheet.build()
                .then(function() {
                    MessageToast.show("Actions exported successfully");
                })
                .catch(function(oError) {
                    MessageBox.error("Failed to export actions: " + oError.message);
                });
        },

        // Helper Methods
        _calculateKPIs: function (aActions) {
            var oActionsModel = this.getView().getModel("actionsModel");
            var oKPIs = {
                totalActions: aActions.length,
                pendingActions: 0,
                inProgressActions: 0,
                completedActions: 0,
                failedActions: 0,
                successRate: 0,
                avgExecutionTime: 0
            };
            
            var iTotalExecutionTime = 0;
            var iCompletedWithTime = 0;
            
            aActions.forEach(function(oAction) {
                switch (oAction.status) {
                    case "PENDING":
                        oKPIs.pendingActions++;
                        break;
                    case "IN_PROGRESS":
                        oKPIs.inProgressActions++;
                        break;
                    case "COMPLETED":
                        oKPIs.completedActions++;
                        if (oAction.executionTime) {
                            iTotalExecutionTime += oAction.executionTime;
                            iCompletedWithTime++;
                        }
                        break;
                    case "FAILED":
                        oKPIs.failedActions++;
                        break;
                }
            });
            
            // Calculate success rate
            var iTotalFinished = oKPIs.completedActions + oKPIs.failedActions;
            if (iTotalFinished > 0) {
                oKPIs.successRate = (oKPIs.completedActions / iTotalFinished) * 100;
            }
            
            // Calculate average execution time
            if (iCompletedWithTime > 0) {
                oKPIs.avgExecutionTime = iTotalExecutionTime / iCompletedWithTime;
            }
            
            oActionsModel.setProperty("/kpis", oKPIs);
        },

        _applyFilters: function () {
            var oActionsModel = this.getView().getModel("actionsModel");
            var oFilters = oActionsModel.getProperty("/filters");
            var aAllActions = oActionsModel.getProperty("/actions");
            
            if (!aAllActions) return;
            
            var aFilteredActions = aAllActions.filter(function(oAction) {
                // Action type filter
                if (oFilters.actionType && oAction.actionType !== oFilters.actionType) {
                    return false;
                }
                
                // Status filter
                if (oFilters.status && oAction.status !== oFilters.status) {
                    return false;
                }
                
                // Priority filter
                if (oFilters.priority && oAction.priority !== oFilters.priority) {
                    return false;
                }
                
                // Date range filter
                if (oFilters.dateRange) {
                    var oDateRange = oFilters.dateRange;
                    var oActionDate = new Date(oAction.createdAt);
                    if (oActionDate < oDateRange.from || oActionDate > oDateRange.to) {
                        return false;
                    }
                }
                
                return true;
            });
            
            // Update the filtered actions
            oActionsModel.setProperty("/filteredActions", aFilteredActions);
        },

        _showCreateActionDialog: function (sActionType) {
            var sTitle = "Create " + this.formatActionType(sActionType);
            var sMessage = "Create a new " + this.formatActionType(sActionType).toLowerCase() + "?";

            MessageBox.confirm(sMessage, {
                title: sTitle,
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._createAction(sActionType);
                    }
                }.bind(this)
            });
        },

        _showBulkUpdateDialog: function (aSelectedItems) {
            var sMessage = "Update status for " + aSelectedItems.length + " selected actions?";

            MessageBox.confirm(sMessage, {
                title: "Bulk Update Status",
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._bulkUpdateStatus(aSelectedItems);
                    }
                }.bind(this)
            });
        },

        _showActionDetails: function (oAction) {
            var sDetails = "Action Details:\n\n" +
                "Action ID: " + oAction.actionId + "\n" +
                "Type: " + this.formatActionType(oAction.actionType) + "\n" +
                "Plant: " + oAction.plant.plantName + "\n" +
                "Product: " + oAction.product.productName + "\n" +
                "Description: " + oAction.description + "\n" +
                "Priority: " + oAction.priority + "\n" +
                "Status: " + oAction.status + "\n" +
                "Progress: " + oAction.progress + "%\n" +
                "Created: " + this.formatDate(oAction.createdAt) + "\n" +
                "Updated: " + this.formatDate(oAction.updatedAt) + "\n";

            if (oAction.estimatedCompletion) {
                sDetails += "Estimated Completion: " + this.formatDate(oAction.estimatedCompletion) + "\n";
            }

            if (oAction.notes) {
                sDetails += "Notes: " + oAction.notes;
            }

            MessageBox.information(sDetails, {
                title: "Action Details"
            });
        },

        _showUpdateStatusDialog: function (oAction) {
            var sMessage = "Update status for action '" + oAction.actionId + "'?";

            MessageBox.confirm(sMessage, {
                title: "Update Action Status",
                onClose: function(sAction) {
                    if (sAction === MessageBox.Action.OK) {
                        this._updateActionStatus(oAction);
                    }
                }.bind(this)
            });
        },

        _createAction: function (sActionType) {
            MessageToast.show("Creating " + this.formatActionType(sActionType).toLowerCase() + "...");

            // Simulate action creation
            setTimeout(function() {
                MessageToast.show("Action created successfully");
                this._loadActions();
            }.bind(this), 2000);
        },

        _bulkUpdateStatus: function (aSelectedItems) {
            MessageToast.show("Updating status for " + aSelectedItems.length + " actions...");

            // Simulate bulk update
            setTimeout(function() {
                MessageToast.show("Bulk status update completed");
                this._loadActions();
            }.bind(this), 2000);
        },

        _updateActionStatus: function (oAction) {
            var oModel = this.getView().getModel();

            // Call update action service
            oModel.callFunction("/updateActionStatus", {
                urlParameters: {
                    actionId: oAction.ID,
                    newStatus: "IN_PROGRESS"
                },
                success: function (oData) {
                    MessageToast.show("Action status updated successfully");
                    this._loadActions();
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to update action status: " + oError.message);
                }.bind(this)
            });
        },

        _cancelAction: function (oAction) {
            var oModel = this.getView().getModel();

            // Call cancel action service
            oModel.callFunction("/cancelAction", {
                urlParameters: {
                    actionId: oAction.ID
                },
                success: function (oData) {
                    MessageToast.show("Action cancelled successfully");
                    this._loadActions();
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to cancel action: " + oError.message);
                }.bind(this)
            });
        },

        // Formatters
        formatActionType: function (sActionType) {
            switch (sActionType) {
                case "STOCK_REALLOCATION": return "Stock Reallocation";
                case "PROCUREMENT_REQUEST": return "Procurement Request";
                case "PRODUCTION_ORDER": return "Production Order";
                default: return sActionType;
            }
        },

        formatActionTypeState: function (sActionType) {
            switch (sActionType) {
                case "STOCK_REALLOCATION": return ValueState.Information;
                case "PROCUREMENT_REQUEST": return ValueState.Warning;
                case "PRODUCTION_ORDER": return ValueState.Success;
                default: return ValueState.None;
            }
        },

        formatPriorityState: function (sPriority) {
            switch (sPriority) {
                case "HIGH": return ValueState.Error;
                case "MEDIUM": return ValueState.Warning;
                case "LOW": return ValueState.Success;
                default: return ValueState.None;
            }
        },

        formatStatusState: function (sStatus) {
            switch (sStatus) {
                case "COMPLETED": return ValueState.Success;
                case "IN_PROGRESS": return ValueState.Warning;
                case "PENDING": return ValueState.Information;
                case "FAILED": return ValueState.Error;
                case "CANCELLED": return ValueState.None;
                default: return ValueState.None;
            }
        },

        formatProgressState: function (iProgress) {
            if (iProgress >= 100) return ValueState.Success;
            if (iProgress >= 50) return ValueState.Warning;
            if (iProgress > 0) return ValueState.Information;
            return ValueState.None;
        },

        formatProgressDisplay: function (iProgress) {
            return (iProgress || 0) + "%";
        },

        formatPercentage: function (fValue) {
            return Math.round(fValue || 0) + "%";
        },

        formatDuration: function (iMinutes) {
            if (!iMinutes) return "0h";
            if (iMinutes < 60) return iMinutes + "m";
            var iHours = Math.floor(iMinutes / 60);
            var iRemainingMinutes = iMinutes % 60;
            return iHours + "h" + (iRemainingMinutes > 0 ? " " + iRemainingMinutes + "m" : "");
        },

        formatDate: function (sDate) {
            if (!sDate) return "";
            var oDate = new Date(sDate);
            return oDate.toLocaleDateString() + " " + oDate.toLocaleTimeString();
        }
    });
});

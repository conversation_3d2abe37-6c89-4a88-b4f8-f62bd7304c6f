sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState) {
    "use strict";

    return Controller.extend("reconciliation.controller.Dashboard", {
        onInit: function () {
            // Initialize dashboard model
            this._initializeDashboardModel();

            // Load initial data
            this._loadDashboardData();

            // Set up refresh timer (every 5 minutes)
            this._setupAutoRefresh();
        },

        _initializeDashboardModel: function () {
            var oDashboardModel = new JSONModel({
                kpis: {
                    totalVariance: 0,
                    shortageCount: 0,
                    surplusCount: 0,
                    pendingApprovals: 0,
                    autoTriggeredActions: 0,
                    avgConfidenceScore: 0,
                    varianceTrend: "None"
                },
                recentReconciliations: [],
                loading: false
            });
            this.getView().setModel(oDashboardModel, "dashboardModel");
        },

        _loadDashboardData: function () {
            var oModel = this.getView().getModel();
            var oDashboardModel = this.getView().getModel("dashboardModel");

            oDashboardModel.setProperty("/loading", true);

            // Load KPIs
            this._loadKPIs();

            // Load recent reconciliations
            this._loadRecentReconciliations();
        },

        _loadKPIs: function () {
            var oModel = this.getView().getModel();
            var oDashboardModel = this.getView().getModel("dashboardModel");

            // Get current date range (last 30 days)
            var oToDate = new Date();
            var oFromDate = new Date();
            oFromDate.setDate(oFromDate.getDate() - 30);

            // Call the getDashboardKPIs function
            oModel.callFunction("/getDashboardKPIs", {
                urlParameters: {
                    plantCode: "",
                    dateFrom: oFromDate.toISOString().split('T')[0],
                    dateTo: oToDate.toISOString().split('T')[0]
                },
                success: function (oData) {
                    oDashboardModel.setProperty("/kpis", {
                        totalVariance: oData.totalVariance || 0,
                        shortageCount: oData.shortageCount || 0,
                        surplusCount: oData.surplusCount || 0,
                        pendingApprovals: oData.pendingApprovals || 0,
                        autoTriggeredActions: oData.autoTriggeredActions || 0,
                        avgConfidenceScore: oData.avgConfidenceScore || 0,
                        varianceTrend: oData.totalVariance > 0 ? "Up" : oData.totalVariance < 0 ? "Down" : "None"
                    });
                    oDashboardModel.setProperty("/loading", false);
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load dashboard KPIs");
                    oDashboardModel.setProperty("/loading", false);
                    console.error("KPI loading error:", oError);
                }.bind(this)
            });
        },

        _loadRecentReconciliations: function () {
            var oModel = this.getView().getModel();
            var oDashboardModel = this.getView().getModel("dashboardModel");

            // Load recent reconciliation results
            oModel.read("/ReconciliationResults", {
                urlParameters: {
                    "$expand": "plant,product",
                    "$orderby": "reconciliationDate desc",
                    "$top": 10
                },
                success: function (oData) {
                    oDashboardModel.setProperty("/recentReconciliations", oData.results);
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load recent reconciliations");
                    console.error("Recent reconciliations loading error:", oError);
                }.bind(this)
            });
        },

        _setupAutoRefresh: function () {
            // Auto-refresh every 5 minutes
            setInterval(function () {
                this._loadDashboardData();
            }.bind(this), 300000);
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onKPITilePress: function (oEvent) {
            var sTileId = oEvent.getSource().getId();
            var sRoute = "";

            if (sTileId.includes("variance")) {
                sRoute = "RouteAnalytics";
            } else if (sTileId.includes("shortage") || sTileId.includes("surplus")) {
                sRoute = "RouteReconciliation";
            } else if (sTileId.includes("confidence")) {
                sRoute = "RouteRecommendations";
            }

            if (sRoute) {
                var oRouter = this.getOwnerComponent().getRouter();
                oRouter.navTo(sRoute);
            }
        },

        onNavigateToUpload: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteUpload");
        },

        onNavigateToReconciliation: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteReconciliation");
        },

        onNavigateToRecommendations: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteRecommendations");
        },

        onNavigateToActions: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteActions");
        },

        onRefreshDashboard: function () {
            MessageToast.show("Refreshing dashboard...");
            this._loadDashboardData();
        },

        onReconciliationItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("dashboardModel");
            var oReconciliation = oBindingContext.getObject();

            // Navigate to reconciliation details or show popup
            MessageBox.information("Reconciliation Details:\n" +
                "Plant: " + oReconciliation.plant.plantName + "\n" +
                "Product: " + oReconciliation.product.productName + "\n" +
                "Variance: " + oReconciliation.variance + "\n" +
                "Status: " + oReconciliation.status);
        },

        onViewReconciliationDetails: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("dashboardModel");
            var oReconciliation = oBindingContext.getObject();

            // Navigate to detailed view
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteReconciliation", {
                reconciliationId: oReconciliation.ID
            });
        },

        // Formatters
        formatVarianceColor: function (fVariance) {
            if (!fVariance) return ValueState.None;
            if (fVariance < -100) return ValueState.Error;
            if (fVariance < 0) return ValueState.Warning;
            if (fVariance > 100) return ValueState.Success;
            return ValueState.None;
        },

        formatTrendIndicator: function (sTrend) {
            switch (sTrend) {
                case "Up": return "Up";
                case "Down": return "Down";
                default: return "None";
            }
        },

        formatPercentage: function (fValue) {
            if (!fValue) return "0%";
            return Math.round(fValue * 100) + "%";
        },

        formatConfidenceColor: function (fConfidence) {
            if (!fConfidence) return ValueState.None;
            if (fConfidence >= 0.8) return ValueState.Success;
            if (fConfidence >= 0.5) return ValueState.Warning;
            return ValueState.Error;
        },

        formatVarianceState: function (fVariance) {
            if (!fVariance) return ValueState.None;
            if (fVariance < -100) return ValueState.Error;
            if (fVariance < 0) return ValueState.Warning;
            if (fVariance > 100) return ValueState.Success;
            return ValueState.None;
        },

        formatStatusState: function (sStatus) {
            switch (sStatus) {
                case "SURPLUS": return ValueState.Success;
                case "SHORTAGE": return ValueState.Error;
                case "BALANCED": return ValueState.Success;
                default: return ValueState.None;
            }
        }
    });
});
sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState",
    "sap/ui/export/Spreadsheet"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState, Spreadsheet) {
    "use strict";

    return Controller.extend("reconciliation.controller.Recommendations", {
        onInit: function () {
            // Initialize recommendations model
            this._initializeRecommendationsModel();
            
            // Load recommendations data
            this._loadRecommendations();
            
            // Set up route matched handler
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.getRoute("RouteRecommendations").attachPatternMatched(this._onRouteMatched, this);
        },

        _initializeRecommendationsModel: function () {
            var oRecommendationsModel = new JSONModel({
                recommendations: [],
                filters: {
                    status: "",
                    confidence: "",
                    actionType: ""
                },
                hasSelectedItems: false,
                hasApprovedItems: false,
                statistics: {
                    total: 0,
                    pending: 0,
                    approved: 0,
                    executed: 0,
                    rejected: 0,
                    avgConfidence: 0
                },
                insights: {
                    topInsight: "AI has identified 15 high-confidence recommendations that could resolve 80% of current variances.",
                    trendInsight: "Stock reallocation recommendations have shown 95% success rate in the past month.",
                    alertInsight: ""
                }
            });
            this.getView().setModel(oRecommendationsModel, "recommendationsModel");
        },

        _onRouteMatched: function (oEvent) {
            var oArguments = oEvent.getParameter("arguments");
            
            // If specific plant/product parameters are passed, filter recommendations
            if (oArguments.plantId && oArguments.productId) {
                this._filterRecommendationsByContext(oArguments.plantId, oArguments.productId);
            }
        },

        _loadRecommendations: function () {
            var oModel = this.getView().getModel();
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            
            // Load AI recommendations
            oModel.read("/AIRecommendations", {
                urlParameters: {
                    "$expand": "plant,product,reconciliationResult",
                    "$orderby": "priority desc, confidenceScore desc"
                },
                success: function (oData) {
                    var aRecommendations = oData.results || [];
                    oRecommendationsModel.setProperty("/recommendations", aRecommendations);
                    
                    // Calculate statistics
                    this._calculateStatistics(aRecommendations);
                    
                    // Update insights
                    this._updateInsights(aRecommendations);
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load recommendations");
                    console.error("Recommendations loading error:", oError);
                }.bind(this)
            });
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onFilterChange: function () {
            this._applyFilters();
        },

        onClearFilters: function () {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            oRecommendationsModel.setProperty("/filters", {
                status: "",
                confidence: "",
                actionType: ""
            });
            this._applyFilters();
        },

        onRefreshRecommendations: function () {
            MessageToast.show("Refreshing recommendations...");
            this._loadRecommendations();
        },

        onSelectionChange: function (oEvent) {
            var oTable = oEvent.getSource();
            var aSelectedItems = oTable.getSelectedItems();
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            
            var bHasSelectedItems = aSelectedItems.length > 0;
            var bHasApprovedItems = false;
            
            // Check if any selected items are approved
            aSelectedItems.forEach(function(oItem) {
                var oContext = oItem.getBindingContext("recommendationsModel");
                var oRecommendation = oContext.getObject();
                if (oRecommendation.status === "APPROVED") {
                    bHasApprovedItems = true;
                }
            });
            
            oRecommendationsModel.setProperty("/hasSelectedItems", bHasSelectedItems);
            oRecommendationsModel.setProperty("/hasApprovedItems", bHasApprovedItems);
        },

        onApproveSelected: function () {
            var oTable = this.byId("recommendationsTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select recommendations to approve");
                return;
            }
            
            MessageBox.confirm(
                "Approve " + aSelectedItems.length + " selected recommendations?",
                {
                    title: "Approve Recommendations",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkApproveRecommendations(aSelectedItems);
                        }
                    }.bind(this)
                }
            );
        },

        onRejectSelected: function () {
            var oTable = this.byId("recommendationsTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            if (aSelectedItems.length === 0) {
                MessageToast.show("Please select recommendations to reject");
                return;
            }
            
            MessageBox.confirm(
                "Reject " + aSelectedItems.length + " selected recommendations?",
                {
                    title: "Reject Recommendations",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkRejectRecommendations(aSelectedItems);
                        }
                    }.bind(this)
                }
            );
        },

        onExecuteSelected: function () {
            var oTable = this.byId("recommendationsTable");
            var aSelectedItems = oTable.getSelectedItems();
            
            // Filter only approved items
            var aApprovedItems = aSelectedItems.filter(function(oItem) {
                var oContext = oItem.getBindingContext("recommendationsModel");
                var oRecommendation = oContext.getObject();
                return oRecommendation.status === "APPROVED";
            });
            
            if (aApprovedItems.length === 0) {
                MessageToast.show("Please select approved recommendations to execute");
                return;
            }
            
            MessageBox.confirm(
                "Execute " + aApprovedItems.length + " approved recommendations?",
                {
                    title: "Execute Recommendations",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._bulkExecuteRecommendations(aApprovedItems);
                        }
                    }.bind(this)
                }
            );
        },

        onGenerateNewRecommendations: function () {
            MessageBox.confirm(
                "Generate new AI recommendations based on current data?",
                {
                    title: "Generate New Recommendations",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._generateNewRecommendations();
                        }
                    }.bind(this)
                }
            );
        },

        onRecommendationItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("recommendationsModel");
            var oRecommendation = oBindingContext.getObject();
            
            this._showRecommendationDetails(oRecommendation);
        },

        onViewRecommendationDetails: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("recommendationsModel");
            var oRecommendation = oBindingContext.getObject();
            
            this._showRecommendationDetails(oRecommendation);
        },

        onApproveRecommendation: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("recommendationsModel");
            var oRecommendation = oBindingContext.getObject();
            
            this._approveRecommendation(oRecommendation);
        },

        onRejectRecommendation: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("recommendationsModel");
            var oRecommendation = oBindingContext.getObject();
            
            this._rejectRecommendation(oRecommendation);
        },

        onExecuteRecommendation: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("recommendationsModel");
            var oRecommendation = oBindingContext.getObject();
            
            this._executeRecommendation(oRecommendation);
        },

        onExportRecommendations: function () {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            var aRecommendations = oRecommendationsModel.getProperty("/recommendations");
            
            if (!aRecommendations || aRecommendations.length === 0) {
                MessageToast.show("No recommendations to export");
                return;
            }

            // Prepare data for export
            var aExportData = aRecommendations.map(function(oRecommendation) {
                return {
                    Priority: oRecommendation.priority,
                    Plant: oRecommendation.plant.plantName,
                    Product: oRecommendation.product.productName,
                    ActionType: oRecommendation.actionType,
                    Title: oRecommendation.title,
                    Description: oRecommendation.description,
                    Confidence: Math.round(oRecommendation.confidenceScore * 100) + "%",
                    Status: oRecommendation.status,
                    CreatedAt: oRecommendation.createdAt
                };
            });

            // Create and download spreadsheet
            var oSpreadsheet = new Spreadsheet({
                workbook: {
                    columns: [
                        { label: "Priority", property: "Priority" },
                        { label: "Plant", property: "Plant" },
                        { label: "Product", property: "Product" },
                        { label: "Action Type", property: "ActionType" },
                        { label: "Title", property: "Title" },
                        { label: "Description", property: "Description" },
                        { label: "Confidence", property: "Confidence" },
                        { label: "Status", property: "Status" },
                        { label: "Created At", property: "CreatedAt" }
                    ]
                },
                dataSource: aExportData,
                fileName: "AIRecommendations_" + new Date().toISOString().split('T')[0] + ".xlsx"
            });

            oSpreadsheet.build()
                .then(function() {
                    MessageToast.show("Recommendations exported successfully");
                })
                .catch(function(oError) {
                    MessageBox.error("Failed to export recommendations: " + oError.message);
                });
        },

        // Helper Methods
        _calculateStatistics: function (aRecommendations) {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            var oStats = {
                total: aRecommendations.length,
                pending: 0,
                approved: 0,
                executed: 0,
                rejected: 0,
                avgConfidence: 0
            };

            var fTotalConfidence = 0;

            aRecommendations.forEach(function(oRecommendation) {
                switch (oRecommendation.status) {
                    case "PENDING":
                        oStats.pending++;
                        break;
                    case "APPROVED":
                        oStats.approved++;
                        break;
                    case "EXECUTED":
                        oStats.executed++;
                        break;
                    case "REJECTED":
                        oStats.rejected++;
                        break;
                }

                fTotalConfidence += oRecommendation.confidenceScore || 0;
            });

            if (aRecommendations.length > 0) {
                oStats.avgConfidence = fTotalConfidence / aRecommendations.length;
            }

            oRecommendationsModel.setProperty("/statistics", oStats);
        },

        _updateInsights: function (aRecommendations) {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            var oInsights = {
                topInsight: "",
                trendInsight: "",
                alertInsight: ""
            };

            if (aRecommendations.length === 0) {
                oInsights.topInsight = "No recommendations available. Run reconciliation analysis to generate AI recommendations.";
            } else {
                var iHighConfidence = aRecommendations.filter(function(r) { return r.confidenceScore >= 0.8; }).length;
                var iPending = aRecommendations.filter(function(r) { return r.status === "PENDING"; }).length;

                oInsights.topInsight = "AI has identified " + iHighConfidence + " high-confidence recommendations out of " + aRecommendations.length + " total.";

                if (iPending > 10) {
                    oInsights.alertInsight = iPending + " recommendations are pending review. Consider bulk approval for high-confidence items.";
                }

                // Analyze action types
                var oActionTypes = {};
                aRecommendations.forEach(function(r) {
                    oActionTypes[r.actionType] = (oActionTypes[r.actionType] || 0) + 1;
                });

                var sMostCommon = Object.keys(oActionTypes).reduce(function(a, b) {
                    return oActionTypes[a] > oActionTypes[b] ? a : b;
                });

                oInsights.trendInsight = "Most recommended action type: " + this.formatActionType(sMostCommon) + " (" + oActionTypes[sMostCommon] + " recommendations)";
            }

            oRecommendationsModel.setProperty("/insights", oInsights);
        },

        _applyFilters: function () {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            var oFilters = oRecommendationsModel.getProperty("/filters");
            var aAllRecommendations = oRecommendationsModel.getProperty("/recommendations");

            if (!aAllRecommendations) return;

            var aFilteredRecommendations = aAllRecommendations.filter(function(oRecommendation) {
                // Status filter
                if (oFilters.status && oRecommendation.status !== oFilters.status) {
                    return false;
                }

                // Confidence filter
                if (oFilters.confidence) {
                    var fConfidence = oRecommendation.confidenceScore || 0;
                    switch (oFilters.confidence) {
                        case "HIGH":
                            if (fConfidence < 0.8) return false;
                            break;
                        case "MEDIUM":
                            if (fConfidence < 0.5 || fConfidence >= 0.8) return false;
                            break;
                        case "LOW":
                            if (fConfidence >= 0.5) return false;
                            break;
                    }
                }

                // Action type filter
                if (oFilters.actionType && oRecommendation.actionType !== oFilters.actionType) {
                    return false;
                }

                return true;
            });

            // Update the table binding or create filtered model
            // For simplicity, we'll update the recommendations directly
            oRecommendationsModel.setProperty("/filteredRecommendations", aFilteredRecommendations);
        },

        _filterRecommendationsByContext: function (sPlantId, sProductId) {
            var oRecommendationsModel = this.getView().getModel("recommendationsModel");
            var aAllRecommendations = oRecommendationsModel.getProperty("/recommendations");

            var aFilteredRecommendations = aAllRecommendations.filter(function(oRecommendation) {
                return oRecommendation.plant.ID === sPlantId && oRecommendation.product.ID === sProductId;
            });

            oRecommendationsModel.setProperty("/recommendations", aFilteredRecommendations);

            if (aFilteredRecommendations.length > 0) {
                MessageToast.show("Showing recommendations for selected plant and product");
            } else {
                MessageToast.show("No recommendations found for selected plant and product");
            }
        },

        _showRecommendationDetails: function (oRecommendation) {
            var sDetails = "AI Recommendation Details:\n\n" +
                "Priority: " + oRecommendation.priority + "\n" +
                "Plant: " + oRecommendation.plant.plantName + "\n" +
                "Product: " + oRecommendation.product.productName + "\n" +
                "Action Type: " + this.formatActionType(oRecommendation.actionType) + "\n" +
                "Title: " + oRecommendation.title + "\n" +
                "Description: " + oRecommendation.description + "\n" +
                "Confidence: " + Math.round(oRecommendation.confidenceScore * 100) + "%\n" +
                "Status: " + oRecommendation.status + "\n" +
                "Reasoning: " + (oRecommendation.reasoning || "AI analysis based on historical patterns and business rules");

            MessageBox.information(sDetails, {
                title: "Recommendation Details"
            });
        },

        _approveRecommendation: function (oRecommendation) {
            var oModel = this.getView().getModel();

            oModel.callFunction("/approveRecommendation", {
                urlParameters: {
                    recommendationId: oRecommendation.ID
                },
                success: function (oData) {
                    MessageToast.show("Recommendation approved successfully");
                    this._loadRecommendations(); // Refresh data
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to approve recommendation: " + oError.message);
                }.bind(this)
            });
        },

        _rejectRecommendation: function (oRecommendation) {
            var oModel = this.getView().getModel();

            oModel.callFunction("/rejectRecommendation", {
                urlParameters: {
                    recommendationId: oRecommendation.ID
                },
                success: function (oData) {
                    MessageToast.show("Recommendation rejected");
                    this._loadRecommendations(); // Refresh data
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to reject recommendation: " + oError.message);
                }.bind(this)
            });
        },

        _executeRecommendation: function (oRecommendation) {
            var oModel = this.getView().getModel();

            oModel.callFunction("/executeRecommendation", {
                urlParameters: {
                    recommendationId: oRecommendation.ID
                },
                success: function (oData) {
                    MessageToast.show("Recommendation executed successfully");
                    this._loadRecommendations(); // Refresh data
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to execute recommendation: " + oError.message);
                }.bind(this)
            });
        },

        _bulkApproveRecommendations: function (aSelectedItems) {
            var aRecommendationIds = aSelectedItems.map(function(oItem) {
                var oContext = oItem.getBindingContext("recommendationsModel");
                return oContext.getObject().ID;
            });

            // Call bulk approve service
            MessageToast.show("Approving " + aRecommendationIds.length + " recommendations...");

            // Simulate bulk operation
            setTimeout(function() {
                MessageToast.show("Bulk approval completed");
                this._loadRecommendations();
            }.bind(this), 2000);
        },

        _bulkRejectRecommendations: function (aSelectedItems) {
            var aRecommendationIds = aSelectedItems.map(function(oItem) {
                var oContext = oItem.getBindingContext("recommendationsModel");
                return oContext.getObject().ID;
            });

            MessageToast.show("Rejecting " + aRecommendationIds.length + " recommendations...");

            // Simulate bulk operation
            setTimeout(function() {
                MessageToast.show("Bulk rejection completed");
                this._loadRecommendations();
            }.bind(this), 2000);
        },

        _bulkExecuteRecommendations: function (aSelectedItems) {
            var aRecommendationIds = aSelectedItems.map(function(oItem) {
                var oContext = oItem.getBindingContext("recommendationsModel");
                return oContext.getObject().ID;
            });

            MessageToast.show("Executing " + aRecommendationIds.length + " recommendations...");

            // Simulate bulk operation
            setTimeout(function() {
                MessageToast.show("Bulk execution completed");
                this._loadRecommendations();
            }.bind(this), 3000);
        },

        _generateNewRecommendations: function () {
            MessageToast.show("Generating new AI recommendations...");

            // Simulate AI generation process
            setTimeout(function() {
                MessageToast.show("New recommendations generated successfully");
                this._loadRecommendations();
            }.bind(this), 5000);
        },

        // Formatters
        formatActionType: function (sActionType) {
            switch (sActionType) {
                case "STOCK_REALLOCATION": return "Stock Reallocation";
                case "PROCUREMENT_REQUEST": return "Procurement Request";
                case "PRODUCTION_ORDER": return "Production Order";
                default: return sActionType;
            }
        },

        formatPriorityState: function (sPriority) {
            switch (sPriority) {
                case "HIGH": return ValueState.Error;
                case "MEDIUM": return ValueState.Warning;
                case "LOW": return ValueState.Success;
                default: return ValueState.None;
            }
        },

        formatConfidencePercent: function (fConfidence) {
            return Math.round((fConfidence || 0) * 100);
        },

        formatConfidenceDisplay: function (fConfidence) {
            return Math.round((fConfidence || 0) * 100) + "%";
        },

        formatConfidenceState: function (fConfidence) {
            if (!fConfidence) return ValueState.None;
            if (fConfidence >= 0.8) return ValueState.Success;
            if (fConfidence >= 0.5) return ValueState.Warning;
            return ValueState.Error;
        },

        formatStatusState: function (sStatus) {
            switch (sStatus) {
                case "APPROVED": return ValueState.Success;
                case "EXECUTED": return ValueState.Success;
                case "REJECTED": return ValueState.Error;
                case "PENDING": return ValueState.Warning;
                default: return ValueState.None;
            }
        }
    });
});

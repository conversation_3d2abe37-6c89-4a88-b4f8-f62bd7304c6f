sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState) {
    "use strict";

    return Controller.extend("reconciliation.controller.Upload", {
        onInit: function () {
            // Initialize upload model
            this._initializeUploadModel();
            
            // Load master data
            this._loadMasterData();
        },

        _initializeUploadModel: function () {
            var oUploadModel = new JSONModel({
                selectedDataType: "demand",
                selectedMethod: 0, // 0 = file upload, 1 = manual entry
                dataValid: false,
                showResults: false,
                resultMessage: "",
                resultType: "Information",
                uploadResults: [],
                demandData: {
                    demandDate: null,
                    plantId: "",
                    productId: "",
                    quantity: "",
                    priority: "MEDIUM",
                    customerOrder: "",
                    dueDate: null,
                    demandDateState: ValueState.None,
                    plantState: ValueState.None,
                    productState: ValueState.None,
                    quantityState: ValueState.None
                },
                supplyData: {
                    supplyDate: null,
                    plantId: "",
                    productId: "",
                    quantity: "",
                    sourceType: "PRODUCTION",
                    sourceRef: "",
                    availableDate: null,
                    supplyDateState: ValueState.None,
                    plantState: ValueState.None,
                    productState: ValueState.None,
                    quantityState: ValueState.None
                },
                stockData: {
                    plantId: "",
                    productId: "",
                    quantity: "",
                    reservedQty: "0",
                    availableQty: "",
                    plantState: ValueState.None,
                    productState: ValueState.None,
                    quantityState: ValueState.None
                }
            });
            this.getView().setModel(oUploadModel, "uploadModel");
        },

        _loadMasterData: function () {
            var oModel = this.getView().getModel();
            
            // Load Plants
            oModel.read("/Plants", {
                success: function (oData) {
                    // Plants loaded successfully
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load plants data");
                    console.error("Plants loading error:", oError);
                }.bind(this)
            });

            // Load Products
            oModel.read("/Products", {
                success: function (oData) {
                    // Products loaded successfully
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load products data");
                    console.error("Products loading error:", oError);
                }.bind(this)
            });
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onDataTypeChange: function (oEvent) {
            var sSelectedKey = oEvent.getParameter("item").getKey();
            var oUploadModel = this.getView().getModel("uploadModel");
            oUploadModel.setProperty("/selectedDataType", sSelectedKey);
            
            // Clear form data when switching data types
            this._clearFormData();
        },

        onUploadMethodChange: function (oEvent) {
            var iSelectedIndex = oEvent.getParameter("selectedIndex");
            var oUploadModel = this.getView().getModel("uploadModel");
            oUploadModel.setProperty("/selectedMethod", iSelectedIndex);
            
            // Clear results when switching methods
            oUploadModel.setProperty("/showResults", false);
        },

        onBeforeItemAdded: function (oEvent) {
            var oItem = oEvent.getParameter("item");
            var oUploadModel = this.getView().getModel("uploadModel");
            var sDataType = oUploadModel.getProperty("/selectedDataType");
            
            // Set custom data for the upload
            oItem.addHeaderField(new sap.ui.unified.FileUploaderParameter({
                name: "dataType",
                value: sDataType
            }));
        },

        onAfterItemAdded: function (oEvent) {
            var oItem = oEvent.getParameter("item");
            MessageToast.show("File added: " + oItem.getFileName());
        },

        onUploadCompleted: function (oEvent) {
            var oItem = oEvent.getParameter("item");
            var sResponse = oEvent.getParameter("response");
            var oUploadModel = this.getView().getModel("uploadModel");
            
            try {
                var oResult = JSON.parse(sResponse);
                if (oResult.success) {
                    MessageToast.show("Upload completed successfully");
                    oUploadModel.setProperty("/showResults", true);
                    oUploadModel.setProperty("/resultMessage", 
                        "Successfully processed " + oResult.recordsProcessed + " records");
                    oUploadModel.setProperty("/resultType", "Success");
                    
                    if (oResult.errors && oResult.errors.length > 0) {
                        var aResults = oResult.errors.map(function(sError, iIndex) {
                            return {
                                row: iIndex + 1,
                                status: "Error",
                                message: sError
                            };
                        });
                        oUploadModel.setProperty("/uploadResults", aResults);
                    }
                } else {
                    MessageBox.error("Upload failed: " + oResult.message);
                    oUploadModel.setProperty("/showResults", true);
                    oUploadModel.setProperty("/resultMessage", "Upload failed: " + oResult.message);
                    oUploadModel.setProperty("/resultType", "Error");
                }
            } catch (e) {
                MessageBox.error("Failed to process upload response");
                console.error("Upload response parsing error:", e);
            }
        },

        onUploadTerminated: function (oEvent) {
            MessageToast.show("Upload terminated");
        },

        onDownloadTemplate: function (oEvent) {
            var oUploadModel = this.getView().getModel("uploadModel");
            var sDataType = oUploadModel.getProperty("/selectedDataType");
            
            // Create and download template based on data type
            var aTemplateData = this._generateTemplateData(sDataType);
            var sFileName = sDataType + "_template.csv";
            
            this._downloadCSV(aTemplateData, sFileName);
        },

        onValidateData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            var sDataType = oUploadModel.getProperty("/selectedDataType");
            var bValid = false;
            
            switch (sDataType) {
                case "demand":
                    bValid = this._validateDemandData();
                    break;
                case "supply":
                    bValid = this._validateSupplyData();
                    break;
                case "stock":
                    bValid = this._validateStockData();
                    break;
            }
            
            oUploadModel.setProperty("/dataValid", bValid);
            
            if (bValid) {
                MessageToast.show("Data validation successful");
            } else {
                MessageToast.show("Please correct the validation errors");
            }
        },

        onSaveData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            var sDataType = oUploadModel.getProperty("/selectedDataType");
            var oModel = this.getView().getModel();
            
            var oData = this._prepareDataForSave(sDataType);
            var sEntitySet = "/" + this._getEntitySetName(sDataType);
            
            oModel.create(sEntitySet, oData, {
                success: function (oCreatedData) {
                    MessageToast.show("Data saved successfully");
                    this._clearFormData();
                    oUploadModel.setProperty("/dataValid", false);
                }.bind(this),
                error: function (oError) {
                    MessageBox.error("Failed to save data: " + oError.message);
                    console.error("Save error:", oError);
                }.bind(this)
            });
        },

        onClearForm: function () {
            this._clearFormData();
            var oUploadModel = this.getView().getModel("uploadModel");
            oUploadModel.setProperty("/dataValid", false);
            oUploadModel.setProperty("/showResults", false);
            MessageToast.show("Form cleared");
        },

        // Helper Methods
        _clearFormData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            
            // Reset demand data
            oUploadModel.setProperty("/demandData", {
                demandDate: null,
                plantId: "",
                productId: "",
                quantity: "",
                priority: "MEDIUM",
                customerOrder: "",
                dueDate: null,
                demandDateState: ValueState.None,
                plantState: ValueState.None,
                productState: ValueState.None,
                quantityState: ValueState.None
            });
            
            // Reset supply data
            oUploadModel.setProperty("/supplyData", {
                supplyDate: null,
                plantId: "",
                productId: "",
                quantity: "",
                sourceType: "PRODUCTION",
                sourceRef: "",
                availableDate: null,
                supplyDateState: ValueState.None,
                plantState: ValueState.None,
                productState: ValueState.None,
                quantityState: ValueState.None
            });
            
            // Reset stock data
            oUploadModel.setProperty("/stockData", {
                plantId: "",
                productId: "",
                quantity: "",
                reservedQty: "0",
                availableQty: "",
                plantState: ValueState.None,
                productState: ValueState.None,
                quantityState: ValueState.None
            });
        },

        _validateDemandData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            var oDemandData = oUploadModel.getProperty("/demandData");
            var bValid = true;
            
            // Validate required fields
            if (!oDemandData.demandDate) {
                oUploadModel.setProperty("/demandData/demandDateState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/demandData/demandDateState", ValueState.None);
            }
            
            if (!oDemandData.plantId) {
                oUploadModel.setProperty("/demandData/plantState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/demandData/plantState", ValueState.None);
            }
            
            if (!oDemandData.productId) {
                oUploadModel.setProperty("/demandData/productState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/demandData/productState", ValueState.None);
            }
            
            if (!oDemandData.quantity || parseFloat(oDemandData.quantity) <= 0) {
                oUploadModel.setProperty("/demandData/quantityState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/demandData/quantityState", ValueState.None);
            }
            
            return bValid;
        },

        _validateSupplyData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            var oSupplyData = oUploadModel.getProperty("/supplyData");
            var bValid = true;
            
            // Validate required fields
            if (!oSupplyData.supplyDate) {
                oUploadModel.setProperty("/supplyData/supplyDateState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/supplyData/supplyDateState", ValueState.None);
            }
            
            if (!oSupplyData.plantId) {
                oUploadModel.setProperty("/supplyData/plantState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/supplyData/plantState", ValueState.None);
            }
            
            if (!oSupplyData.productId) {
                oUploadModel.setProperty("/supplyData/productState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/supplyData/productState", ValueState.None);
            }
            
            if (!oSupplyData.quantity || parseFloat(oSupplyData.quantity) <= 0) {
                oUploadModel.setProperty("/supplyData/quantityState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/supplyData/quantityState", ValueState.None);
            }
            
            return bValid;
        },

        _validateStockData: function () {
            var oUploadModel = this.getView().getModel("uploadModel");
            var oStockData = oUploadModel.getProperty("/stockData");
            var bValid = true;

            // Validate required fields
            if (!oStockData.plantId) {
                oUploadModel.setProperty("/stockData/plantState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/stockData/plantState", ValueState.None);
            }

            if (!oStockData.productId) {
                oUploadModel.setProperty("/stockData/productState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/stockData/productState", ValueState.None);
            }

            if (!oStockData.quantity || parseFloat(oStockData.quantity) < 0) {
                oUploadModel.setProperty("/stockData/quantityState", ValueState.Error);
                bValid = false;
            } else {
                oUploadModel.setProperty("/stockData/quantityState", ValueState.None);
            }

            return bValid;
        },

        _prepareDataForSave: function (sDataType) {
            var oUploadModel = this.getView().getModel("uploadModel");
            var oData = {};

            switch (sDataType) {
                case "demand":
                    var oDemandData = oUploadModel.getProperty("/demandData");
                    oData = {
                        demandDate: oDemandData.demandDate,
                        plant_ID: oDemandData.plantId,
                        product_ID: oDemandData.productId,
                        quantity: parseFloat(oDemandData.quantity),
                        priority: oDemandData.priority,
                        customerOrder: oDemandData.customerOrder,
                        dueDate: oDemandData.dueDate
                    };
                    break;
                case "supply":
                    var oSupplyData = oUploadModel.getProperty("/supplyData");
                    oData = {
                        supplyDate: oSupplyData.supplyDate,
                        plant_ID: oSupplyData.plantId,
                        product_ID: oSupplyData.productId,
                        quantity: parseFloat(oSupplyData.quantity),
                        sourceType: oSupplyData.sourceType,
                        sourceRef: oSupplyData.sourceRef,
                        availableDate: oSupplyData.availableDate
                    };
                    break;
                case "stock":
                    var oStockData = oUploadModel.getProperty("/stockData");
                    oData = {
                        plant_ID: oStockData.plantId,
                        product_ID: oStockData.productId,
                        quantity: parseFloat(oStockData.quantity),
                        reservedQty: parseFloat(oStockData.reservedQty) || 0,
                        availableQty: parseFloat(oStockData.availableQty) || parseFloat(oStockData.quantity)
                    };
                    break;
            }

            return oData;
        },

        _getEntitySetName: function (sDataType) {
            switch (sDataType) {
                case "demand": return "Demand";
                case "supply": return "Supply";
                case "stock": return "Stock";
                default: return "";
            }
        },

        _generateTemplateData: function (sDataType) {
            var aTemplateData = [];

            switch (sDataType) {
                case "demand":
                    aTemplateData = [
                        ["demandDate", "plantCode", "productCode", "quantity", "priority", "customerOrder", "dueDate"],
                        ["2024-01-15", "P001", "PROD001", "100", "HIGH", "CO12345", "2024-01-20"],
                        ["2024-01-16", "P002", "PROD002", "200", "MEDIUM", "CO12346", "2024-01-25"]
                    ];
                    break;
                case "supply":
                    aTemplateData = [
                        ["supplyDate", "plantCode", "productCode", "quantity", "sourceType", "sourceRef", "availableDate"],
                        ["2024-01-15", "P001", "PROD001", "150", "PRODUCTION", "PO12345", "2024-01-18"],
                        ["2024-01-16", "P002", "PROD002", "250", "PROCUREMENT", "PO12346", "2024-01-22"]
                    ];
                    break;
                case "stock":
                    aTemplateData = [
                        ["plantCode", "productCode", "quantity", "reservedQty", "availableQty"],
                        ["P001", "PROD001", "500", "50", "450"],
                        ["P002", "PROD002", "300", "30", "270"]
                    ];
                    break;
            }

            return aTemplateData;
        },

        _downloadCSV: function (aData, sFileName) {
            var sCsvContent = aData.map(function(aRow) {
                return aRow.join(",");
            }).join("\n");

            var oBlob = new Blob([sCsvContent], { type: "text/csv;charset=utf-8;" });
            var oUrl = URL.createObjectURL(oBlob);

            var oLink = document.createElement("a");
            oLink.setAttribute("href", oUrl);
            oLink.setAttribute("download", sFileName);
            oLink.style.visibility = "hidden";
            document.body.appendChild(oLink);
            oLink.click();
            document.body.removeChild(oLink);

            MessageToast.show("Template downloaded: " + sFileName);
        },

        // Formatters
        formatResultStatus: function (sStatus) {
            switch (sStatus) {
                case "Success": return ValueState.Success;
                case "Error": return ValueState.Error;
                case "Warning": return ValueState.Warning;
                default: return ValueState.None;
            }
        }
    });
});

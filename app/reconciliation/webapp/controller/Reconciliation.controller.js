sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox",
    "sap/ui/core/ValueState",
    "sap/ui/export/Spreadsheet"
], function (Controller, JSONModel, MessageToast, MessageBox, ValueState, Spreadsheet) {
    "use strict";

    return Controller.extend("reconciliation.controller.Reconciliation", {
        onInit: function () {
            // Initialize reconciliation model
            this._initializeReconciliationModel();
            
            // Load master data
            this._loadMasterData();
            
            // Set default analysis date to today
            var oToday = new Date();
            this.getView().getModel("reconciliationModel").setProperty("/parameters/analysisDate", oToday);
        },

        _initializeReconciliationModel: function () {
            var oReconciliationModel = new JSONModel({
                parameters: {
                    analysisDate: null,
                    selectedPlants: [],
                    selectedCategories: [],
                    analysisType: "FULL",
                    varianceTolerance: 5,
                    generateRecommendations: true,
                    analysisDateState: ValueState.None
                },
                parametersValid: false,
                analysisRunning: false,
                analysisProgress: 0,
                progressText: "",
                progressStatus: "",
                showResults: false,
                results: {
                    summary: {
                        totalVariance: 0,
                        shortageItems: 0,
                        surplusItems: 0,
                        balancedItems: 0,
                        varianceTrend: "None"
                    },
                    items: []
                },
                filters: {
                    status: "",
                    plant: "",
                    productSearch: ""
                }
            });
            this.getView().setModel(oReconciliationModel, "reconciliationModel");
        },

        _loadMasterData: function () {
            var oModel = this.getView().getModel();
            
            // Load Plants
            oModel.read("/Plants", {
                success: function (oData) {
                    // Plants loaded successfully
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load plants data");
                    console.error("Plants loading error:", oError);
                }.bind(this)
            });

            // Load Product Categories
            oModel.read("/Products", {
                urlParameters: {
                    "$select": "category",
                    "$orderby": "category"
                },
                success: function (oData) {
                    // Extract unique categories
                    var aCategories = [];
                    var oCategories = {};
                    oData.results.forEach(function(oProduct) {
                        if (oProduct.category && !oCategories[oProduct.category]) {
                            oCategories[oProduct.category] = true;
                            aCategories.push({
                                ID: oProduct.category,
                                categoryName: oProduct.category
                            });
                        }
                    });
                    
                    var oCategoryModel = new JSONModel(aCategories);
                    this.getView().setModel(oCategoryModel, "categoriesModel");
                }.bind(this),
                error: function (oError) {
                    MessageToast.show("Failed to load product categories");
                    console.error("Categories loading error:", oError);
                }.bind(this)
            });
        },

        // Event Handlers
        onNavBack: function () {
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteDashboard");
        },

        onPlantSelectionChange: function (oEvent) {
            this._validateParameters();
        },

        onCategorySelectionChange: function (oEvent) {
            this._validateParameters();
        },

        onRunAnalysis: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var oParameters = oReconciliationModel.getProperty("/parameters");
            
            if (!this._validateParameters()) {
                MessageToast.show("Please correct the parameter validation errors");
                return;
            }

            // Start analysis
            oReconciliationModel.setProperty("/analysisRunning", true);
            oReconciliationModel.setProperty("/analysisProgress", 0);
            oReconciliationModel.setProperty("/progressText", "Initializing analysis...");
            oReconciliationModel.setProperty("/progressStatus", "Starting reconciliation analysis");
            oReconciliationModel.setProperty("/showResults", false);

            // Call the reconciliation service
            this._executeReconciliation(oParameters);
        },

        onClearParameters: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            oReconciliationModel.setProperty("/parameters", {
                analysisDate: new Date(),
                selectedPlants: [],
                selectedCategories: [],
                analysisType: "FULL",
                varianceTolerance: 5,
                generateRecommendations: true,
                analysisDateState: ValueState.None
            });
            oReconciliationModel.setProperty("/parametersValid", false);
            oReconciliationModel.setProperty("/showResults", false);
            MessageToast.show("Parameters cleared");
        },

        onSaveParameters: function () {
            // Save parameters to local storage or user preferences
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var oParameters = oReconciliationModel.getProperty("/parameters");
            
            localStorage.setItem("reconciliationParameters", JSON.stringify(oParameters));
            MessageToast.show("Parameters saved");
        },

        onCancelAnalysis: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            oReconciliationModel.setProperty("/analysisRunning", false);
            oReconciliationModel.setProperty("/analysisProgress", 0);
            MessageToast.show("Analysis cancelled");
        },

        onSummaryTilePress: function (oEvent) {
            var sTileId = oEvent.getSource().getId();
            var sFilter = "";
            
            if (sTileId.includes("shortage")) {
                sFilter = "SHORTAGE";
            } else if (sTileId.includes("surplus")) {
                sFilter = "SURPLUS";
            } else if (sTileId.includes("balanced")) {
                sFilter = "BALANCED";
            }
            
            if (sFilter) {
                var oReconciliationModel = this.getView().getModel("reconciliationModel");
                oReconciliationModel.setProperty("/filters/status", sFilter);
                this._applyFilters();
            }
        },

        onFilterChange: function () {
            this._applyFilters();
        },

        onProductSearch: function () {
            this._applyFilters();
        },

        onClearFilters: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            oReconciliationModel.setProperty("/filters", {
                status: "",
                plant: "",
                productSearch: ""
            });
            this._applyFilters();
        },

        onResultItemPress: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("reconciliationModel");
            var oItem = oBindingContext.getObject();
            
            // Show item details in a dialog
            this._showItemDetails(oItem);
        },

        onViewDetails: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("reconciliationModel");
            var oItem = oBindingContext.getObject();
            
            this._showItemDetails(oItem);
        },

        onGetRecommendation: function (oEvent) {
            var oBindingContext = oEvent.getSource().getBindingContext("reconciliationModel");
            var oItem = oBindingContext.getObject();
            
            // Navigate to recommendations view with this item
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteRecommendations", {
                plantId: oItem.plant.ID,
                productId: oItem.product.ID
            });
        },

        onExportResults: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var aResults = oReconciliationModel.getProperty("/results/items");
            
            if (!aResults || aResults.length === 0) {
                MessageToast.show("No results to export");
                return;
            }

            // Prepare data for export
            var aExportData = aResults.map(function(oItem) {
                return {
                    Plant: oItem.plant.plantName,
                    Product: oItem.product.productName,
                    Demand: oItem.demandQty,
                    Supply: oItem.supplyQty,
                    Stock: oItem.stockQty,
                    Variance: oItem.variance,
                    Status: oItem.status,
                    UnitOfMeasure: oItem.product.unitOfMeasure
                };
            });

            // Create and download spreadsheet
            var oSpreadsheet = new Spreadsheet({
                workbook: {
                    columns: [
                        { label: "Plant", property: "Plant" },
                        { label: "Product", property: "Product" },
                        { label: "Demand", property: "Demand", type: "Number" },
                        { label: "Supply", property: "Supply", type: "Number" },
                        { label: "Stock", property: "Stock", type: "Number" },
                        { label: "Variance", property: "Variance", type: "Number" },
                        { label: "Status", property: "Status" },
                        { label: "Unit of Measure", property: "UnitOfMeasure" }
                    ]
                },
                dataSource: aExportData,
                fileName: "ReconciliationResults_" + new Date().toISOString().split('T')[0] + ".xlsx"
            });

            oSpreadsheet.build()
                .then(function() {
                    MessageToast.show("Results exported successfully");
                })
                .catch(function(oError) {
                    MessageBox.error("Failed to export results: " + oError.message);
                });
        },

        onGenerateRecommendations: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var aResults = oReconciliationModel.getProperty("/results/items");
            
            // Filter items that need recommendations (not balanced)
            var aItemsNeedingRecommendations = aResults.filter(function(oItem) {
                return oItem.status !== "BALANCED";
            });
            
            if (aItemsNeedingRecommendations.length === 0) {
                MessageToast.show("No items require recommendations");
                return;
            }

            MessageBox.confirm(
                "Generate AI recommendations for " + aItemsNeedingRecommendations.length + " items?",
                {
                    title: "Generate Recommendations",
                    onClose: function(sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._generateBulkRecommendations(aItemsNeedingRecommendations);
                        }
                    }.bind(this)
                }
            );
        },

        // Helper Methods
        _validateParameters: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var oParameters = oReconciliationModel.getProperty("/parameters");
            var bValid = true;

            // Validate analysis date
            if (!oParameters.analysisDate) {
                oReconciliationModel.setProperty("/parameters/analysisDateState", ValueState.Error);
                bValid = false;
            } else {
                oReconciliationModel.setProperty("/parameters/analysisDateState", ValueState.None);
            }

            oReconciliationModel.setProperty("/parametersValid", bValid);
            return bValid;
        },

        _executeReconciliation: function (oParameters) {
            var oModel = this.getView().getModel();
            var oReconciliationModel = this.getView().getModel("reconciliationModel");

            // Simulate progress updates
            this._simulateProgress();

            // Call the runReconciliation action
            oModel.callFunction("/runReconciliation", {
                urlParameters: {
                    analysisDate: oParameters.analysisDate.toISOString().split('T')[0],
                    plantCodes: oParameters.selectedPlants.join(","),
                    productCategories: oParameters.selectedCategories.join(","),
                    analysisType: oParameters.analysisType,
                    varianceTolerance: oParameters.varianceTolerance,
                    generateRecommendations: oParameters.generateRecommendations
                },
                success: function (oData) {
                    this._handleReconciliationSuccess(oData);
                }.bind(this),
                error: function (oError) {
                    this._handleReconciliationError(oError);
                }.bind(this)
            });
        },

        _simulateProgress: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var iProgress = 0;
            var aSteps = [
                { progress: 20, text: "Loading demand data...", status: "Analyzing demand requirements" },
                { progress: 40, text: "Loading supply data...", status: "Analyzing supply availability" },
                { progress: 60, text: "Loading stock data...", status: "Analyzing current stock levels" },
                { progress: 80, text: "Calculating variances...", status: "Computing demand-supply variances" },
                { progress: 100, text: "Finalizing results...", status: "Preparing reconciliation results" }
            ];

            var fnUpdateProgress = function(iIndex) {
                if (iIndex < aSteps.length && oReconciliationModel.getProperty("/analysisRunning")) {
                    var oStep = aSteps[iIndex];
                    oReconciliationModel.setProperty("/analysisProgress", oStep.progress);
                    oReconciliationModel.setProperty("/progressText", oStep.text);
                    oReconciliationModel.setProperty("/progressStatus", oStep.status);

                    setTimeout(function() {
                        fnUpdateProgress(iIndex + 1);
                    }, 1000);
                }
            };

            fnUpdateProgress(0);
        },

        _handleReconciliationSuccess: function (oData) {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");

            // Stop progress simulation
            oReconciliationModel.setProperty("/analysisRunning", false);
            oReconciliationModel.setProperty("/analysisProgress", 100);
            oReconciliationModel.setProperty("/progressText", "Analysis completed");

            // Process results
            var aResults = oData.results || [];
            var oSummary = this._calculateSummary(aResults);

            oReconciliationModel.setProperty("/results/summary", oSummary);
            oReconciliationModel.setProperty("/results/items", aResults);
            oReconciliationModel.setProperty("/showResults", true);

            MessageToast.show("Reconciliation analysis completed successfully");
        },

        _handleReconciliationError: function (oError) {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");

            oReconciliationModel.setProperty("/analysisRunning", false);
            oReconciliationModel.setProperty("/analysisProgress", 0);

            MessageBox.error("Reconciliation analysis failed: " + oError.message);
            console.error("Reconciliation error:", oError);
        },

        _calculateSummary: function (aResults) {
            var oSummary = {
                totalVariance: 0,
                shortageItems: 0,
                surplusItems: 0,
                balancedItems: 0,
                varianceTrend: "None"
            };

            aResults.forEach(function(oItem) {
                oSummary.totalVariance += Math.abs(oItem.variance || 0);

                switch (oItem.status) {
                    case "SHORTAGE":
                        oSummary.shortageItems++;
                        break;
                    case "SURPLUS":
                        oSummary.surplusItems++;
                        break;
                    case "BALANCED":
                        oSummary.balancedItems++;
                        break;
                }
            });

            return oSummary;
        },

        _applyFilters: function () {
            var oReconciliationModel = this.getView().getModel("reconciliationModel");
            var oFilters = oReconciliationModel.getProperty("/filters");
            var aAllItems = oReconciliationModel.getProperty("/results/items");

            if (!aAllItems) return;

            var aFilteredItems = aAllItems.filter(function(oItem) {
                // Status filter
                if (oFilters.status && oItem.status !== oFilters.status) {
                    return false;
                }

                // Plant filter
                if (oFilters.plant && oItem.plant.ID !== oFilters.plant) {
                    return false;
                }

                // Product search
                if (oFilters.productSearch) {
                    var sSearch = oFilters.productSearch.toLowerCase();
                    var sProductName = (oItem.product.productName || "").toLowerCase();
                    var sProductCode = (oItem.product.productCode || "").toLowerCase();
                    if (sProductName.indexOf(sSearch) === -1 && sProductCode.indexOf(sSearch) === -1) {
                        return false;
                    }
                }

                return true;
            });

            // Update the binding or create a filtered model
            // For simplicity, we'll update the items directly
            oReconciliationModel.setProperty("/results/filteredItems", aFilteredItems);
        },

        _showItemDetails: function (oItem) {
            var sDetails = "Reconciliation Details:\n\n" +
                "Plant: " + oItem.plant.plantName + " (" + oItem.plant.plantCode + ")\n" +
                "Product: " + oItem.product.productName + " (" + oItem.product.productCode + ")\n" +
                "Demand: " + oItem.demandQty + " " + oItem.product.unitOfMeasure + "\n" +
                "Supply: " + oItem.supplyQty + " " + oItem.product.unitOfMeasure + "\n" +
                "Stock: " + oItem.stockQty + " " + oItem.product.unitOfMeasure + "\n" +
                "Variance: " + oItem.variance + " " + oItem.product.unitOfMeasure + "\n" +
                "Status: " + oItem.status;

            MessageBox.information(sDetails, {
                title: "Item Details"
            });
        },

        _generateBulkRecommendations: function (aItems) {
            MessageToast.show("Generating recommendations for " + aItems.length + " items...");

            // Navigate to recommendations view
            var oRouter = this.getOwnerComponent().getRouter();
            oRouter.navTo("RouteRecommendations");
        },

        // Formatters
        formatVarianceColor: function (fVariance) {
            if (!fVariance) return ValueState.None;
            if (Math.abs(fVariance) > 100) return ValueState.Error;
            if (Math.abs(fVariance) > 50) return ValueState.Warning;
            return ValueState.Success;
        },

        formatTrendIndicator: function (sTrend) {
            switch (sTrend) {
                case "Up": return "Up";
                case "Down": return "Down";
                default: return "None";
            }
        },

        formatVarianceState: function (fVariance) {
            if (!fVariance) return ValueState.None;
            if (fVariance < -50) return ValueState.Error;
            if (fVariance < 0) return ValueState.Warning;
            if (fVariance > 50) return ValueState.Success;
            return ValueState.None;
        },

        formatStatusState: function (sStatus) {
            switch (sStatus) {
                case "SURPLUS": return ValueState.Success;
                case "SHORTAGE": return ValueState.Error;
                case "BALANCED": return ValueState.Success;
                default: return ValueState.None;
            }
        }
    });
});

<mvc:View controllerName="reconciliation.controller.Recommendations"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:core="sap.ui.core">
    <Page id="recommendationsPage" title="{i18n>recommendationsTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="recommendationsScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- AI Recommendations Header -->
                <VBox id="headerSection" class="sapUiMediumMargin">
                    <Title id="headerTitle" text="AI-Powered Reconciliation Recommendations" level="H2" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="aiInfoStrip" 
                        text="Our AI analyzes demand-supply patterns, historical data, and business rules to provide intelligent recommendations for resolving variances."
                        type="Information"
                        class="sapUiMediumMarginBottom"/>
                </VBox>

                <!-- Filter and Control Section -->
                <VBox id="controlSection" class="sapUiMediumMargin">
                    <Panel id="controlPanel" headerText="Recommendation Filters" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="filterBar" class="sapUiMediumMargin">
                                <ComboBox id="statusFilter" 
                                    placeholder="Filter by Status"
                                    selectedKey="{recommendationsModel>/filters/status}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Status"/>
                                    <core:Item key="PENDING" text="Pending Review"/>
                                    <core:Item key="APPROVED" text="Approved"/>
                                    <core:Item key="REJECTED" text="Rejected"/>
                                    <core:Item key="EXECUTED" text="Executed"/>
                                </ComboBox>
                                <ComboBox id="confidenceFilter" 
                                    placeholder="Filter by Confidence"
                                    selectedKey="{recommendationsModel>/filters/confidence}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Confidence Levels"/>
                                    <core:Item key="HIGH" text="High (>80%)"/>
                                    <core:Item key="MEDIUM" text="Medium (50-80%)"/>
                                    <core:Item key="LOW" text="Low (<50%)"/>
                                </ComboBox>
                                <ComboBox id="actionTypeFilter" 
                                    placeholder="Filter by Action Type"
                                    selectedKey="{recommendationsModel>/filters/actionType}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item key="" text="All Action Types"/>
                                    <core:Item key="STOCK_REALLOCATION" text="Stock Reallocation"/>
                                    <core:Item key="PROCUREMENT_REQUEST" text="Procurement Request"/>
                                    <core:Item key="PRODUCTION_ORDER" text="Production Order"/>
                                </ComboBox>
                                <Button id="clearFiltersButton" 
                                    text="{i18n>clearFilters}" 
                                    icon="sap-icon://clear-filter"
                                    type="Transparent" 
                                    press="onClearFilters"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="refreshRecommendationsButton" 
                                    text="{i18n>refresh}" 
                                    icon="sap-icon://refresh"
                                    type="Default" 
                                    press="onRefreshRecommendations"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Bulk Actions Section -->
                <VBox id="bulkActionsSection" class="sapUiMediumMargin">
                    <Panel id="bulkActionsPanel" headerText="Bulk Actions" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="bulkActionsBar" class="sapUiMediumMargin">
                                <Button id="approveSelectedButton" 
                                    text="Approve Selected" 
                                    icon="sap-icon://accept"
                                    type="Accept" 
                                    press="onApproveSelected"
                                    enabled="{recommendationsModel>/hasSelectedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="rejectSelectedButton" 
                                    text="Reject Selected" 
                                    icon="sap-icon://decline"
                                    type="Reject" 
                                    press="onRejectSelected"
                                    enabled="{recommendationsModel>/hasSelectedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="executeSelectedButton" 
                                    text="Execute Selected" 
                                    icon="sap-icon://play"
                                    type="Emphasized" 
                                    press="onExecuteSelected"
                                    enabled="{recommendationsModel>/hasApprovedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="generateNewButton" 
                                    text="Generate New Recommendations" 
                                    icon="sap-icon://ai"
                                    type="Default" 
                                    press="onGenerateNewRecommendations"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Recommendations Table -->
                <VBox id="recommendationsTableSection" class="sapUiMediumMargin">
                    <Table id="recommendationsTable"
                        items="{recommendationsModel>/recommendations}"
                        mode="MultiSelect"
                        selectionChange="onSelectionChange"
                        class="sapUiResponsiveMargin"
                        growing="true"
                        growingThreshold="50">
                        <headerToolbar>
                            <Toolbar id="recommendationsToolbar">
                                <Title id="recommendationsTableTitle" text="AI Recommendations ({recommendationsModel>/recommendations/length})"/>
                                <ToolbarSpacer id="recommendationsToolbarSpacer"/>
                                <Button id="exportRecommendationsButton" 
                                    text="{i18n>export}" 
                                    icon="sap-icon://excel-attachment"
                                    type="Transparent" 
                                    press="onExportRecommendations"/>
                            </Toolbar>
                        </headerToolbar>
                        <columns>
                            <Column id="priorityColumn" sortProperty="priority">
                                <Text id="priorityColumnHeader" text="Priority"/>
                            </Column>
                            <Column id="plantColumn" sortProperty="plant/plantName">
                                <Text id="plantColumnHeader" text="Plant"/>
                            </Column>
                            <Column id="productColumn" sortProperty="product/productName">
                                <Text id="productColumnHeader" text="Product"/>
                            </Column>
                            <Column id="actionTypeColumn" sortProperty="actionType">
                                <Text id="actionTypeColumnHeader" text="Action Type"/>
                            </Column>
                            <Column id="recommendationColumn">
                                <Text id="recommendationColumnHeader" text="Recommendation"/>
                            </Column>
                            <Column id="confidenceColumn" sortProperty="confidenceScore">
                                <Text id="confidenceColumnHeader" text="Confidence"/>
                            </Column>
                            <Column id="statusColumn" sortProperty="status">
                                <Text id="statusColumnHeader" text="Status"/>
                            </Column>
                            <Column id="actionsColumn">
                                <Text id="actionsColumnHeader" text="Actions"/>
                            </Column>
                        </columns>
                        <items>
                            <ColumnListItem id="recommendationListItem" press="onRecommendationItemPress">
                                <ObjectStatus id="priorityStatus" 
                                    text="{recommendationsModel>priority}" 
                                    state="{path: 'recommendationsModel>priority', formatter: '.formatPriorityState'}"/>
                                <Text id="plantText" text="{recommendationsModel>plant/plantName}"/>
                                <Text id="productText" text="{recommendationsModel>product/productName}"/>
                                <Text id="actionTypeText" text="{path: 'recommendationsModel>actionType', formatter: '.formatActionType'}"/>
                                <VBox id="recommendationContent">
                                    <Text id="recommendationTitle" text="{recommendationsModel>title}" class="sapUiTinyMarginBottom"/>
                                    <Text id="recommendationDescription" text="{recommendationsModel>description}" class="sapMText"/>
                                </VBox>
                                <VBox id="confidenceContent">
                                    <ProgressIndicator id="confidenceProgress" 
                                        percentValue="{path: 'recommendationsModel>confidenceScore', formatter: '.formatConfidencePercent'}" 
                                        displayValue="{path: 'recommendationsModel>confidenceScore', formatter: '.formatConfidenceDisplay'}"
                                        state="{path: 'recommendationsModel>confidenceScore', formatter: '.formatConfidenceState'}"
                                        width="100px"/>
                                </VBox>
                                <ObjectStatus id="statusObject" 
                                    text="{recommendationsModel>status}" 
                                    state="{path: 'recommendationsModel>status', formatter: '.formatStatusState'}"/>
                                <HBox id="actionButtons">
                                    <Button id="viewDetailsButton" 
                                        icon="sap-icon://detail-view" 
                                        type="Transparent" 
                                        press="onViewRecommendationDetails"
                                        tooltip="View Details"/>
                                    <Button id="approveButton" 
                                        icon="sap-icon://accept" 
                                        type="Accept" 
                                        press="onApproveRecommendation"
                                        tooltip="Approve"
                                        visible="{= ${recommendationsModel>status} === 'PENDING'}"/>
                                    <Button id="rejectButton" 
                                        icon="sap-icon://decline" 
                                        type="Reject" 
                                        press="onRejectRecommendation"
                                        tooltip="Reject"
                                        visible="{= ${recommendationsModel>status} === 'PENDING'}"/>
                                    <Button id="executeButton" 
                                        icon="sap-icon://play" 
                                        type="Emphasized" 
                                        press="onExecuteRecommendation"
                                        tooltip="Execute"
                                        visible="{= ${recommendationsModel>status} === 'APPROVED'}"/>
                                </HBox>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>

                <!-- AI Insights Section -->
                <VBox id="insightsSection" class="sapUiMediumMargin">
                    <Title id="insightsTitle" text="AI Insights & Analytics" level="H3" class="sapUiMediumMarginBottom"/>
                    <Panel id="insightsPanel" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="insightsContent" class="sapUiMediumMargin">
                                <VBox id="insightsStats" class="sapUiTinyMargin">
                                    <Title id="statsTitle" text="Recommendation Statistics" level="H4" class="sapUiTinyMarginBottom"/>
                                    <VBox id="statsContent">
                                        <HBox id="totalRecommendationsBox" class="sapUiTinyMarginBottom">
                                            <Text id="totalRecommendationsLabel" text="Total Recommendations: " class="sapUiTinyMarginEnd"/>
                                            <Text id="totalRecommendationsValue" text="{recommendationsModel>/statistics/total}"/>
                                        </HBox>
                                        <HBox id="pendingRecommendationsBox" class="sapUiTinyMarginBottom">
                                            <Text id="pendingRecommendationsLabel" text="Pending Review: " class="sapUiTinyMarginEnd"/>
                                            <Text id="pendingRecommendationsValue" text="{recommendationsModel>/statistics/pending}"/>
                                        </HBox>
                                        <HBox id="approvedRecommendationsBox" class="sapUiTinyMarginBottom">
                                            <Text id="approvedRecommendationsLabel" text="Approved: " class="sapUiTinyMarginEnd"/>
                                            <Text id="approvedRecommendationsValue" text="{recommendationsModel>/statistics/approved}"/>
                                        </HBox>
                                        <HBox id="executedRecommendationsBox" class="sapUiTinyMarginBottom">
                                            <Text id="executedRecommendationsLabel" text="Executed: " class="sapUiTinyMarginEnd"/>
                                            <Text id="executedRecommendationsValue" text="{recommendationsModel>/statistics/executed}"/>
                                        </HBox>
                                        <HBox id="avgConfidenceBox" class="sapUiTinyMarginBottom">
                                            <Text id="avgConfidenceLabel" text="Average Confidence: " class="sapUiTinyMarginEnd"/>
                                            <Text id="avgConfidenceValue" text="{path: 'recommendationsModel>/statistics/avgConfidence', formatter: '.formatConfidenceDisplay'}"/>
                                        </HBox>
                                    </VBox>
                                </VBox>
                                <VBox id="insightsRecommendations" class="sapUiTinyMargin">
                                    <Title id="aiInsightsTitle" text="AI Insights" level="H4" class="sapUiTinyMarginBottom"/>
                                    <VBox id="aiInsightsContent">
                                        <MessageStrip id="topInsight" 
                                            text="{recommendationsModel>/insights/topInsight}"
                                            type="Information"
                                            class="sapUiTinyMarginBottom"/>
                                        <MessageStrip id="trendInsight" 
                                            text="{recommendationsModel>/insights/trendInsight}"
                                            type="Success"
                                            class="sapUiTinyMarginBottom"/>
                                        <MessageStrip id="alertInsight" 
                                            text="{recommendationsModel>/insights/alertInsight}"
                                            type="Warning"
                                            visible="{= ${recommendationsModel>/insights/alertInsight} !== ''}"/>
                                    </VBox>
                                </VBox>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>

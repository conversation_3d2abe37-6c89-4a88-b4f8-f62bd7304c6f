<mvc:View controllerName="reconciliation.controller.Upload"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:upload="sap.m.upload">
    <Page id="uploadPage" title="{i18n>uploadTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="uploadScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                <!-- Upload Type Selection -->
                <VBox id="uploadTypeSection" class="sapUiMediumMargin">
                    <Title id="uploadTypeTitle" text="Select Data Type" level="H2" class="sapUiMediumMarginBottom"/>
                    <SegmentedButton id="dataTypeSegmentedButton" 
                        selectedKey="{uploadModel>/selectedDataType}" 
                        selectionChange="onDataTypeChange"
                        class="sapUiMediumMarginBottom">
                        <items>
                            <SegmentedButtonItem id="demandSegment" key="demand" text="{i18n>uploadDemand}" icon="sap-icon://request"/>
                            <SegmentedButtonItem id="supplySegment" key="supply" text="{i18n>uploadSupply}" icon="sap-icon://supply"/>
                            <SegmentedButtonItem id="stockSegment" key="stock" text="{i18n>uploadStock}" icon="sap-icon://inventory"/>
                        </items>
                    </SegmentedButton>
                </VBox>

                <!-- Upload Method Selection -->
                <VBox id="uploadMethodSection" class="sapUiMediumMargin">
                    <Title id="uploadMethodTitle" text="Upload Method" level="H2" class="sapUiMediumMarginBottom"/>
                    <RadioButtonGroup id="uploadMethodGroup" 
                        selectedIndex="{uploadModel>/selectedMethod}" 
                        select="onUploadMethodChange"
                        class="sapUiMediumMarginBottom">
                        <RadioButton id="fileUploadRadio" text="{i18n>uploadFile}" />
                        <RadioButton id="manualEntryRadio" text="{i18n>manualEntry}" />
                    </RadioButtonGroup>
                </VBox>

                <!-- File Upload Section -->
                <VBox id="fileUploadSection" 
                    visible="{= ${uploadModel>/selectedMethod} === 0}"
                    class="sapUiMediumMargin">
                    <Title id="fileUploadTitle" text="File Upload" level="H3" class="sapUiMediumMarginBottom"/>
                    
                    <Panel id="fileUploadPanel" headerText="Upload CSV/Excel File" class="sapUiResponsiveMargin">
                        <content>
                            <VBox id="fileUploadContent" class="sapUiMediumMargin">
                                <upload:UploadSet id="uploadSet"
                                    uploadEnabled="true"
                                    terminationEnabled="true"
                                    fileTypes="csv,xlsx,xls"
                                    maxFileNameLength="100"
                                    maxFileSize="10"
                                    mediaTypes="text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                    uploadUrl="/reconciliation/bulkDataUpload"
                                    uploadCompleted="onUploadCompleted"
                                    uploadTerminated="onUploadTerminated"
                                    beforeItemAdded="onBeforeItemAdded"
                                    afterItemAdded="onAfterItemAdded">
                                </upload:UploadSet>
                                
                                <MessageStrip id="uploadInstructions" 
                                    text="Upload CSV or Excel files with the required columns. Download template files for the correct format."
                                    type="Information"
                                    class="sapUiMediumMarginTop"/>
                                
                                <HBox id="templateDownloadBox" class="sapUiMediumMarginTop">
                                    <Button id="downloadDemandTemplate" 
                                        text="Download Demand Template" 
                                        icon="sap-icon://download"
                                        press="onDownloadTemplate"
                                        visible="{= ${uploadModel>/selectedDataType} === 'demand'}"
                                        class="sapUiTinyMarginEnd"/>
                                    <Button id="downloadSupplyTemplate" 
                                        text="Download Supply Template" 
                                        icon="sap-icon://download"
                                        press="onDownloadTemplate"
                                        visible="{= ${uploadModel>/selectedDataType} === 'supply'}"
                                        class="sapUiTinyMarginEnd"/>
                                    <Button id="downloadStockTemplate" 
                                        text="Download Stock Template" 
                                        icon="sap-icon://download"
                                        press="onDownloadTemplate"
                                        visible="{= ${uploadModel>/selectedDataType} === 'stock'}"
                                        class="sapUiTinyMarginEnd"/>
                                </HBox>
                            </VBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Manual Entry Section -->
                <VBox id="manualEntrySection" 
                    visible="{= ${uploadModel>/selectedMethod} === 1}"
                    class="sapUiMediumMargin">
                    <Title id="manualEntryTitle" text="Manual Data Entry" level="H3" class="sapUiMediumMarginBottom"/>
                    
                    <!-- Demand Form -->
                    <Panel id="demandFormPanel" 
                        headerText="Demand Data Entry" 
                        visible="{= ${uploadModel>/selectedDataType} === 'demand'}"
                        class="sapUiResponsiveMargin">
                        <content>
                            <f:SimpleForm id="demandForm"
                                editable="true"
                                layout="ResponsiveGridLayout"
                                labelSpanXL="3"
                                labelSpanL="3"
                                labelSpanM="3"
                                labelSpanS="12"
                                adjustLabelSpan="false"
                                emptySpanXL="4"
                                emptySpanL="4"
                                emptySpanM="4"
                                emptySpanS="0"
                                columnsXL="1"
                                columnsL="1"
                                columnsM="1"
                                singleContainerFullSize="false"
                                class="sapUiMediumMargin">
                                <f:content>
                                    <Label id="demandDateLabel" text="Demand Date" required="true"/>
                                    <DatePicker id="demandDatePicker" value="{uploadModel>/demandData/demandDate}" valueState="{uploadModel>/demandData/demandDateState}"/>
                                    
                                    <Label id="demandPlantLabel" text="Plant" required="true"/>
                                    <ComboBox id="demandPlantCombo" 
                                        selectedKey="{uploadModel>/demandData/plantId}"
                                        valueState="{uploadModel>/demandData/plantState}"
                                        items="{/Plants}">
                                        <core:Item key="{ID}" text="{plantName} ({plantCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="demandProductLabel" text="Product" required="true"/>
                                    <ComboBox id="demandProductCombo" 
                                        selectedKey="{uploadModel>/demandData/productId}"
                                        valueState="{uploadModel>/demandData/productState}"
                                        items="{/Products}">
                                        <core:Item key="{ID}" text="{productName} ({productCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="demandQuantityLabel" text="Quantity" required="true"/>
                                    <Input id="demandQuantityInput" 
                                        value="{uploadModel>/demandData/quantity}" 
                                        type="Number"
                                        valueState="{uploadModel>/demandData/quantityState}"/>
                                    
                                    <Label id="demandPriorityLabel" text="Priority"/>
                                    <ComboBox id="demandPriorityCombo" selectedKey="{uploadModel>/demandData/priority}">
                                        <core:Item key="HIGH" text="High"/>
                                        <core:Item key="MEDIUM" text="Medium"/>
                                        <core:Item key="LOW" text="Low"/>
                                    </ComboBox>
                                    
                                    <Label id="demandCustomerOrderLabel" text="Customer Order"/>
                                    <Input id="demandCustomerOrderInput" value="{uploadModel>/demandData/customerOrder}"/>
                                    
                                    <Label id="demandDueDateLabel" text="Due Date"/>
                                    <DatePicker id="demandDueDatePicker" value="{uploadModel>/demandData/dueDate}"/>
                                </f:content>
                            </f:SimpleForm>
                        </content>
                    </Panel>

                    <!-- Supply Form -->
                    <Panel id="supplyFormPanel" 
                        headerText="Supply Data Entry" 
                        visible="{= ${uploadModel>/selectedDataType} === 'supply'}"
                        class="sapUiResponsiveMargin">
                        <content>
                            <f:SimpleForm id="supplyForm"
                                editable="true"
                                layout="ResponsiveGridLayout"
                                labelSpanXL="3"
                                labelSpanL="3"
                                labelSpanM="3"
                                labelSpanS="12"
                                adjustLabelSpan="false"
                                emptySpanXL="4"
                                emptySpanL="4"
                                emptySpanM="4"
                                emptySpanS="0"
                                columnsXL="1"
                                columnsL="1"
                                columnsM="1"
                                singleContainerFullSize="false"
                                class="sapUiMediumMargin">
                                <f:content>
                                    <Label id="supplyDateLabel" text="Supply Date" required="true"/>
                                    <DatePicker id="supplyDatePicker" value="{uploadModel>/supplyData/supplyDate}" valueState="{uploadModel>/supplyData/supplyDateState}"/>
                                    
                                    <Label id="supplyPlantLabel" text="Plant" required="true"/>
                                    <ComboBox id="supplyPlantCombo" 
                                        selectedKey="{uploadModel>/supplyData/plantId}"
                                        valueState="{uploadModel>/supplyData/plantState}"
                                        items="{/Plants}">
                                        <core:Item key="{ID}" text="{plantName} ({plantCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="supplyProductLabel" text="Product" required="true"/>
                                    <ComboBox id="supplyProductCombo" 
                                        selectedKey="{uploadModel>/supplyData/productId}"
                                        valueState="{uploadModel>/supplyData/productState}"
                                        items="{/Products}">
                                        <core:Item key="{ID}" text="{productName} ({productCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="supplyQuantityLabel" text="Quantity" required="true"/>
                                    <Input id="supplyQuantityInput" 
                                        value="{uploadModel>/supplyData/quantity}" 
                                        type="Number"
                                        valueState="{uploadModel>/supplyData/quantityState}"/>
                                    
                                    <Label id="supplySourceTypeLabel" text="Source Type"/>
                                    <ComboBox id="supplySourceTypeCombo" selectedKey="{uploadModel>/supplyData/sourceType}">
                                        <core:Item key="PRODUCTION" text="Production"/>
                                        <core:Item key="PROCUREMENT" text="Procurement"/>
                                        <core:Item key="TRANSFER" text="Transfer"/>
                                    </ComboBox>
                                    
                                    <Label id="supplySourceRefLabel" text="Source Reference"/>
                                    <Input id="supplySourceRefInput" value="{uploadModel>/supplyData/sourceRef}"/>
                                    
                                    <Label id="supplyAvailableDateLabel" text="Available Date"/>
                                    <DatePicker id="supplyAvailableDatePicker" value="{uploadModel>/supplyData/availableDate}"/>
                                </f:content>
                            </f:SimpleForm>
                        </content>
                    </Panel>

                    <!-- Stock Form -->
                    <Panel id="stockFormPanel" 
                        headerText="Stock Data Entry" 
                        visible="{= ${uploadModel>/selectedDataType} === 'stock'}"
                        class="sapUiResponsiveMargin">
                        <content>
                            <f:SimpleForm id="stockForm"
                                editable="true"
                                layout="ResponsiveGridLayout"
                                labelSpanXL="3"
                                labelSpanL="3"
                                labelSpanM="3"
                                labelSpanS="12"
                                adjustLabelSpan="false"
                                emptySpanXL="4"
                                emptySpanL="4"
                                emptySpanM="4"
                                emptySpanS="0"
                                columnsXL="1"
                                columnsL="1"
                                columnsM="1"
                                singleContainerFullSize="false"
                                class="sapUiMediumMargin">
                                <f:content>
                                    <Label id="stockPlantLabel" text="Plant" required="true"/>
                                    <ComboBox id="stockPlantCombo" 
                                        selectedKey="{uploadModel>/stockData/plantId}"
                                        valueState="{uploadModel>/stockData/plantState}"
                                        items="{/Plants}">
                                        <core:Item key="{ID}" text="{plantName} ({plantCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="stockProductLabel" text="Product" required="true"/>
                                    <ComboBox id="stockProductCombo" 
                                        selectedKey="{uploadModel>/stockData/productId}"
                                        valueState="{uploadModel>/stockData/productState}"
                                        items="{/Products}">
                                        <core:Item key="{ID}" text="{productName} ({productCode})"/>
                                    </ComboBox>
                                    
                                    <Label id="stockQuantityLabel" text="Stock Quantity" required="true"/>
                                    <Input id="stockQuantityInput" 
                                        value="{uploadModel>/stockData/quantity}" 
                                        type="Number"
                                        valueState="{uploadModel>/stockData/quantityState}"/>
                                    
                                    <Label id="stockReservedLabel" text="Reserved Quantity"/>
                                    <Input id="stockReservedInput" 
                                        value="{uploadModel>/stockData/reservedQty}" 
                                        type="Number"/>
                                    
                                    <Label id="stockAvailableLabel" text="Available Quantity"/>
                                    <Input id="stockAvailableInput" 
                                        value="{uploadModel>/stockData/availableQty}" 
                                        type="Number"/>
                                </f:content>
                            </f:SimpleForm>
                        </content>
                    </Panel>

                    <!-- Action Buttons -->
                    <HBox id="manualEntryActions" class="sapUiMediumMargin">
                        <Button id="validateDataButton" 
                            text="{i18n>validateData}" 
                            icon="sap-icon://validate"
                            type="Emphasized" 
                            press="onValidateData"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="saveDataButton" 
                            text="{i18n>save}" 
                            icon="sap-icon://save"
                            type="Default" 
                            press="onSaveData"
                            enabled="{uploadModel>/dataValid}"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="clearFormButton" 
                            text="{i18n>cancel}" 
                            icon="sap-icon://clear-all"
                            type="Transparent" 
                            press="onClearForm"/>
                    </HBox>
                </VBox>

                <!-- Upload Results Section -->
                <VBox id="uploadResultsSection" 
                    visible="{uploadModel>/showResults}"
                    class="sapUiMediumMargin">
                    <Title id="uploadResultsTitle" text="Upload Results" level="H3" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="uploadResultsMessage" 
                        text="{uploadModel>/resultMessage}"
                        type="{uploadModel>/resultType}"
                        class="sapUiMediumMarginBottom"/>
                    
                    <Table id="uploadResultsTable"
                        items="{uploadModel>/uploadResults}"
                        visible="{= ${uploadModel>/uploadResults}.length > 0}"
                        mode="None">
                        <columns>
                            <Column id="resultRowColumn"><Text id="resultRowText" text="Row"/></Column>
                            <Column id="resultStatusColumn"><Text id="resultStatusText" text="Status"/></Column>
                            <Column id="resultMessageColumn"><Text id="resultMessageText" text="Message"/></Column>
                        </columns>
                        <items>
                            <ColumnListItem id="resultListItem">
                                <Text id="resultRowNumber" text="{uploadModel>row}"/>
                                <ObjectStatus id="resultStatus" 
                                    text="{uploadModel>status}" 
                                    state="{path: 'uploadModel>status', formatter: '.formatResultStatus'}"/>
                                <Text id="resultMessage" text="{uploadModel>message}"/>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>

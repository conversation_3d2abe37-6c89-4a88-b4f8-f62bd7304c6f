<mvc:View controllerName="reconciliation.controller.Analytics"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:suite="sap.suite.ui.commons"
    xmlns:viz="sap.viz"
    xmlns:core="sap.ui.core">
    <Page id="analyticsPage" title="{i18n>analyticsTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="analyticsScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- Analytics Header -->
                <VBox id="headerSection" class="sapUiMediumMargin">
                    <Title id="headerTitle" text="Analytics & Insights" level="H2" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="analyticsInfoStrip" 
                        text="Comprehensive analytics and insights for demand-supply reconciliation performance and trends."
                        type="Information"
                        class="sapUiMediumMarginBottom"/>
                </VBox>

                <!-- Time Period Selection -->
                <VBox id="timePeriodSection" class="sapUiMediumMargin">
                    <Panel id="timePeriodPanel" headerText="Analysis Period" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="timePeriodBar" class="sapUiMediumMargin">
                                <SegmentedButton id="periodSelector" 
                                    selectedKey="{analyticsModel>/selectedPeriod}"
                                    selectionChange="onPeriodChange"
                                    class="sapUiTinyMarginEnd">
                                    <items>
                                        <SegmentedButtonItem id="lastWeekSegment" key="lastWeek" text="Last Week"/>
                                        <SegmentedButtonItem id="lastMonthSegment" key="lastMonth" text="Last Month"/>
                                        <SegmentedButtonItem id="lastQuarterSegment" key="lastQuarter" text="Last Quarter"/>
                                        <SegmentedButtonItem id="lastYearSegment" key="lastYear" text="Last Year"/>
                                        <SegmentedButtonItem id="customSegment" key="custom" text="Custom"/>
                                    </items>
                                </SegmentedButton>
                                <DateRangeSelection id="customDateRange" 
                                    visible="{= ${analyticsModel>/selectedPeriod} === 'custom'}"
                                    value="{analyticsModel>/customDateRange}"
                                    change="onCustomDateChange"
                                    class="sapUiTinyMarginStart"/>
                                <Button id="refreshAnalyticsButton" 
                                    text="{i18n>refresh}" 
                                    icon="sap-icon://refresh"
                                    type="Emphasized" 
                                    press="onRefreshAnalytics"
                                    class="sapUiTinyMarginStart"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Key Performance Indicators -->
                <VBox id="kpiSection" class="sapUiMediumMargin">
                    <Title id="kpiTitle" text="Key Performance Indicators" level="H3" class="sapUiMediumMarginBottom"/>
                    <f:GridContainer id="kpiGridContainer" class="sapUiResponsiveMargin">
                        <f:GridContainerSettings rowSize="5rem" columnSize="5rem" gap="1rem"/>
                        
                        <f:Card id="reconciliationAccuracyKPI" class="dashboardKPITile">
                            <f:header>
                                <card:Header id="accuracyKPIHeader" title="Reconciliation Accuracy"/>
                            </f:header>
                            <f:content>
                                <suite:NumericTile id="accuracyTile" 
                                    headerText="Accuracy Rate"
                                    subHeaderText="Current Period"
                                    value="{analyticsModel>/kpis/reconciliationAccuracy/value}"
                                    valueColor="{analyticsModel>/kpis/reconciliationAccuracy/state}"
                                    indicator="{analyticsModel>/kpis/reconciliationAccuracy/trend}"
                                    size="M"
                                    press="onKPIPress"/>
                            </f:content>
                        </f:Card>

                        <f:Card id="avgResolutionTimeKPI" class="dashboardKPITile">
                            <f:header>
                                <card:Header id="resolutionTimeKPIHeader" title="Avg Resolution Time"/>
                            </f:header>
                            <f:content>
                                <suite:NumericTile id="resolutionTimeTile" 
                                    headerText="Resolution Time"
                                    subHeaderText="Hours"
                                    value="{analyticsModel>/kpis/avgResolutionTime/value}"
                                    valueColor="{analyticsModel>/kpis/avgResolutionTime/state}"
                                    indicator="{analyticsModel>/kpis/avgResolutionTime/trend}"
                                    size="M"
                                    press="onKPIPress"/>
                            </f:content>
                        </f:Card>

                        <f:Card id="aiRecommendationSuccessKPI" class="dashboardKPITile">
                            <f:header>
                                <card:Header id="aiSuccessKPIHeader" title="AI Success Rate"/>
                            </f:header>
                            <f:content>
                                <suite:NumericTile id="aiSuccessTile" 
                                    headerText="AI Success Rate"
                                    subHeaderText="Recommendations"
                                    value="{analyticsModel>/kpis/aiRecommendationSuccess/value}"
                                    valueColor="{analyticsModel>/kpis/aiRecommendationSuccess/state}"
                                    indicator="{analyticsModel>/kpis/aiRecommendationSuccess/trend}"
                                    size="M"
                                    press="onKPIPress"/>
                            </f:content>
                        </f:Card>

                        <f:Card id="costSavingsKPI" class="dashboardKPITile">
                            <f:header>
                                <card:Header id="costSavingsKPIHeader" title="Cost Savings"/>
                            </f:header>
                            <f:content>
                                <suite:NumericTile id="costSavingsTile" 
                                    headerText="Total Savings"
                                    subHeaderText="USD"
                                    value="{analyticsModel>/kpis/costSavings/value}"
                                    valueColor="{analyticsModel>/kpis/costSavings/state}"
                                    indicator="{analyticsModel>/kpis/costSavings/trend}"
                                    size="M"
                                    press="onKPIPress"/>
                            </f:content>
                        </f:Card>
                    </f:GridContainer>
                </VBox>

                <!-- Charts Section -->
                <VBox id="chartsSection" class="sapUiMediumMargin">
                    <Title id="chartsTitle" text="Trend Analysis" level="H3" class="sapUiMediumMarginBottom"/>
                    
                    <!-- Reconciliation Trends Chart -->
                    <Panel id="reconciliationTrendsPanel" headerText="Reconciliation Trends Over Time" class="sapUiResponsiveMargin">
                        <content>
                            <VBox id="reconciliationTrendsContent" class="sapUiMediumMargin">
                                <viz:Popover id="reconciliationTrendsPopover"/>
                                <viz:VizFrame id="reconciliationTrendsChart"
                                    vizType="line"
                                    width="100%"
                                    height="400px">
                                    <viz:dataset>
                                        <viz:FlattenedDataset data="{analyticsModel>/chartData/reconciliationTrends}">
                                            <viz:dimensions>
                                                <viz:DimensionDefinition name="Date" value="{date}"/>
                                            </viz:dimensions>
                                            <viz:measures>
                                                <viz:MeasureDefinition name="Shortages" value="{shortages}"/>
                                                <viz:MeasureDefinition name="Surpluses" value="{surpluses}"/>
                                                <viz:MeasureDefinition name="Balanced" value="{balanced}"/>
                                            </viz:measures>
                                        </viz:FlattenedDataset>
                                    </viz:dataset>
                                </viz:VizFrame>
                            </VBox>
                        </content>
                    </Panel>

                    <!-- Plant Performance Chart -->
                    <Panel id="plantPerformancePanel" headerText="Plant Performance Comparison" class="sapUiResponsiveMargin">
                        <content>
                            <VBox id="plantPerformanceContent" class="sapUiMediumMargin">
                                <viz:Popover id="plantPerformancePopover"/>
                                <viz:VizFrame id="plantPerformanceChart"
                                    vizType="column"
                                    width="100%"
                                    height="400px">
                                    <viz:dataset>
                                        <viz:FlattenedDataset data="{analyticsModel>/chartData/plantPerformance}">
                                            <viz:dimensions>
                                                <viz:DimensionDefinition name="Plant" value="{plantName}"/>
                                            </viz:dimensions>
                                            <viz:measures>
                                                <viz:MeasureDefinition name="Accuracy Rate" value="{accuracyRate}"/>
                                                <viz:MeasureDefinition name="Resolution Time" value="{avgResolutionTime}"/>
                                            </viz:measures>
                                        </viz:FlattenedDataset>
                                    </viz:dataset>
                                </viz:VizFrame>
                            </VBox>
                        </content>
                    </Panel>

                    <!-- AI Recommendations Performance -->
                    <Panel id="aiPerformancePanel" headerText="AI Recommendations Performance" class="sapUiResponsiveMargin">
                        <content>
                            <VBox id="aiPerformanceContent" class="sapUiMediumMargin">
                                <viz:Popover id="aiPerformancePopover"/>
                                <viz:VizFrame id="aiPerformanceChart"
                                    vizType="donut"
                                    width="100%"
                                    height="400px">
                                    <viz:dataset>
                                        <viz:FlattenedDataset data="{analyticsModel>/chartData/aiPerformance}">
                                            <viz:dimensions>
                                                <viz:DimensionDefinition name="Status" value="{status}"/>
                                            </viz:dimensions>
                                            <viz:measures>
                                                <viz:MeasureDefinition name="Count" value="{count}"/>
                                            </viz:measures>
                                        </viz:FlattenedDataset>
                                    </viz:dataset>
                                </viz:VizFrame>
                            </VBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Detailed Analytics Table -->
                <VBox id="detailedAnalyticsSection" class="sapUiMediumMargin">
                    <Title id="detailedAnalyticsTitle" text="Detailed Analytics" level="H3" class="sapUiMediumMarginBottom"/>
                    <Panel id="detailedAnalyticsPanel" class="sapUiResponsiveMargin">
                        <content>
                            <Table id="analyticsTable"
                                items="{analyticsModel>/detailedAnalytics}"
                                class="sapUiResponsiveMargin"
                                growing="true"
                                growingThreshold="50">
                                <headerToolbar>
                                    <Toolbar id="analyticsToolbar">
                                        <Title id="analyticsTableTitle" text="Performance Metrics by Plant"/>
                                        <ToolbarSpacer id="analyticsToolbarSpacer"/>
                                        <Button id="exportAnalyticsButton" 
                                            text="{i18n>export}" 
                                            icon="sap-icon://excel-attachment"
                                            type="Transparent" 
                                            press="onExportAnalytics"/>
                                    </Toolbar>
                                </headerToolbar>
                                
                                <columns>
                                    <Column id="plantColumn" sortProperty="plantName">
                                        <Text id="plantColumnHeader" text="Plant"/>
                                    </Column>
                                    <Column id="totalReconciliationsColumn" sortProperty="totalReconciliations">
                                        <Text id="totalReconciliationsColumnHeader" text="Total Reconciliations"/>
                                    </Column>
                                    <Column id="accuracyRateColumn" sortProperty="accuracyRate">
                                        <Text id="accuracyRateColumnHeader" text="Accuracy Rate"/>
                                    </Column>
                                    <Column id="avgResolutionTimeColumn" sortProperty="avgResolutionTime">
                                        <Text id="avgResolutionTimeColumnHeader" text="Avg Resolution Time"/>
                                    </Column>
                                    <Column id="aiRecommendationsColumn" sortProperty="aiRecommendations">
                                        <Text id="aiRecommendationsColumnHeader" text="AI Recommendations"/>
                                    </Column>
                                    <Column id="costSavingsColumn" sortProperty="costSavings">
                                        <Text id="costSavingsColumnHeader" text="Cost Savings"/>
                                    </Column>
                                    <Column id="lastUpdatedColumn" sortProperty="lastUpdated">
                                        <Text id="lastUpdatedColumnHeader" text="Last Updated"/>
                                    </Column>
                                </columns>
                                
                                <items>
                                    <ColumnListItem id="analyticsListItem" press="onAnalyticsItemPress">
                                        <Link id="plantLink" text="{analyticsModel>plantName}" press="onPlantLinkPress"/>
                                        <Text id="totalReconciliationsText" text="{analyticsModel>totalReconciliations}"/>
                                        <ObjectStatus id="accuracyRateStatus" 
                                            text="{path: 'analyticsModel>accuracyRate', formatter: '.formatPercentage'}" 
                                            state="{path: 'analyticsModel>accuracyRate', formatter: '.formatAccuracyState'}"/>
                                        <Text id="avgResolutionTimeText" text="{path: 'analyticsModel>avgResolutionTime', formatter: '.formatDuration'}"/>
                                        <Text id="aiRecommendationsText" text="{analyticsModel>aiRecommendations}"/>
                                        <Text id="costSavingsText" text="{path: 'analyticsModel>costSavings', formatter: '.formatCurrency'}"/>
                                        <Text id="lastUpdatedText" text="{path: 'analyticsModel>lastUpdated', formatter: '.formatDate'}"/>
                                    </ColumnListItem>
                                </items>
                            </Table>
                        </content>
                    </Panel>
                </VBox>

                <!-- Insights and Recommendations -->
                <VBox id="insightsSection" class="sapUiMediumMargin">
                    <Title id="insightsTitle" text="AI-Generated Insights" level="H3" class="sapUiMediumMarginBottom"/>
                    <Panel id="insightsPanel" class="sapUiResponsiveMargin aiInsightsPanel">
                        <content>
                            <VBox id="insightsContent" class="sapUiMediumMargin">
                                <MessageStrip id="insightsStrip" 
                                    text="{analyticsModel>/insights/summary}"
                                    type="Information"
                                    class="sapUiMediumMarginBottom"/>
                                
                                <VBox id="keyInsightsList" items="{analyticsModel>/insights/keyInsights}">
                                    <HBox id="insightItem" class="sapUiTinyMarginBottom">
                                        <core:Icon id="insightIcon" src="sap-icon://lightbulb" color="#FFD700" class="sapUiTinyMarginEnd"/>
                                        <Text id="insightText" text="{analyticsModel>text}" class="sapUiTinyMarginStart"/>
                                    </HBox>
                                </VBox>
                                
                                <Title id="recommendationsTitle" text="Recommendations" level="H4" class="sapUiMediumMarginTop sapUiTinyMarginBottom"/>
                                <VBox id="recommendationsList" items="{analyticsModel>/insights/recommendations}">
                                    <HBox id="recommendationItem" class="sapUiTinyMarginBottom">
                                        <core:Icon id="recommendationIcon" src="sap-icon://target-group" color="#32CD32" class="sapUiTinyMarginEnd"/>
                                        <Text id="recommendationText" text="{analyticsModel>text}" class="sapUiTinyMarginStart"/>
                                    </HBox>
                                </VBox>
                            </VBox>
                        </content>
                    </Panel>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>

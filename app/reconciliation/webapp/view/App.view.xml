<mvc:View controllerName="reconciliation.controller.App"
    displayBlock="true"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f">
    <f:ShellBar id="shellBar"
        title="{i18n>appTitle}"
        homeIcon="sap-icon://factory"
        showNavButton="false"
        showMenuButton="true"
        menuButtonPressed="onMenuButtonPress">
        <f:profile>
            <Avatar id="avatar"
                displaySize="S"
                press="onAvatarPress"/>
        </f:profile>
    </f:ShellBar>
    <App id="app">
    </App>
</mvc:View>
{"_version": "1.65.0", "sap.app": {"id": "reconciliation", "type": "application", "i18n": "i18n/i18n.properties", "applicationVersion": {"version": "1.0.0"}, "title": "{{appTitle}}", "description": "{{appDescription}}", "resources": "resources.json", "sourceTemplate": {"id": "@sap/generator-fiori:basic", "version": "1.18.3", "toolsId": "913db63d-1fb8-43b2-9ccb-9ff6eedb78ab"}, "dataSources": {"mainService": {"uri": "/reconciliation/", "type": "OData", "settings": {"annotations": [], "odataVersion": "4.0"}}, "adminService": {"uri": "/admin/", "type": "OData", "settings": {"annotations": [], "odataVersion": "4.0"}}, "analyticsService": {"uri": "/analytics/", "type": "OData", "settings": {"annotations": [], "odataVersion": "4.0"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "sap-icon://factory", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"flexEnabled": true, "dependencies": {"minUI5Version": "1.138.1", "libs": {"sap.m": {}, "sap.ui.core": {}, "sap.f": {}, "sap.suite.ui.commons": {}, "sap.ui.table": {}, "sap.ui.unified": {}, "sap.viz": {}}}, "contentDensities": {"compact": true, "cozy": true}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "settings": {"bundleName": "reconciliation.i18n.i18n"}}, "": {"dataSource": "mainService", "preload": true, "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true}}, "admin": {"dataSource": "adminService", "preload": false, "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": false}}, "analytics": {"dataSource": "analyticsService", "preload": false, "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": false}}}, "resources": {"css": [{"uri": "css/style.css"}]}, "routing": {"config": {"routerClass": "sap.m.routing.Router", "controlAggregation": "pages", "controlId": "app", "transition": "slide", "type": "View", "viewType": "XML", "path": "reconciliation.view", "async": true, "viewPath": "reconciliation.view"}, "routes": [{"name": "RouteDashboard", "pattern": ":?query:", "target": ["TargetDashboard"]}, {"name": "RouteUpload", "pattern": "upload", "target": ["TargetUpload"]}, {"name": "RouteReconciliation", "pattern": "reconciliation", "target": ["TargetReconciliation"]}, {"name": "RouteRecommendations", "pattern": "recommendations", "target": ["TargetRecommendations"]}, {"name": "RouteActions", "pattern": "actions", "target": ["TargetActions"]}, {"name": "RouteMasterData", "pattern": "masterdata/{entity}", "target": ["TargetMasterData"]}, {"name": "RouteAnalytics", "pattern": "analytics", "target": ["TargetAnalytics"]}], "targets": {"TargetDashboard": {"id": "Dashboard", "name": "Dashboard"}, "TargetUpload": {"id": "Upload", "name": "Upload"}, "TargetReconciliation": {"id": "Reconciliation", "name": "Reconciliation"}, "TargetRecommendations": {"id": "Recommendations", "name": "Recommendations"}, "TargetActions": {"id": "Actions", "name": "Actions"}, "TargetMasterData": {"id": "MasterData", "name": "MasterData"}, "TargetAnalytics": {"id": "Analytics", "name": "Analytics"}}}, "rootView": {"viewName": "reconciliation.view.App", "type": "XML", "id": "App", "async": true}}}
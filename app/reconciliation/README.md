## Application Details
|               |
| ------------- |
|**Generation Date and Time**<br>Thu Jul 31 2025 09:37:11 GMT+0000 (Coordinated Universal Time)|
|**App Generator**<br>SAP Fiori Application Generator|
|**App Generator Version**<br>1.18.3|
|**Generation Platform**<br>SAP Business Application Studio|
|**Template Used**<br>Basic V4|
|**Service Type**<br>Local CAP|
|**Service URL**<br>http://localhost:4004/reconciliation/|
|**Module Name**<br>reconciliation|
|**Application Title**<br>Demand and Supply Reconciliation|
|**Namespace**<br>|
|**UI5 Theme**<br>sap_horizon|
|**UI5 Version**<br>1.138.1|
|**Enable Code Assist Libraries**<br>False|
|**Enable TypeScript**<br>False|
|**Add Eslint configuration**<br>False|

## reconciliation

An SAP Fiori application.

### Starting the generated app

-   This app has been generated using the SAP Fiori tools - App Generator, as part of the SAP Fiori tools suite.  To launch the generated app, start your CAP project:  and navigate to the following location in your browser:

http://localhost:4004/reconciliation/webapp/index.html

#### Pre-requisites:

1. Active NodeJS LTS (Long Term Support) version and associated supported NPM version.  (See https://nodejs.org)



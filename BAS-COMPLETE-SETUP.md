# 🚀 Complete SAP BAS Setup Guide - Demand-Supply Reconciliation System

## 📋 **Quick Start in BAS (5 Minutes)**

### **Step 1: Open Terminal in BAS**
```bash
# 1. Install all dependencies
npm install

# 2. Deploy database with sample data
cds deploy --to sqlite

# 3. Start the application
cds watch --port 4004
```

### **Step 2: Access the Application**
1. **BAS will show a popup**: "A service is listening on port 4004"
2. **Click**: "Expose and Open" 
3. **Navigate to**: `/reconciliation-dashboard/webapp/index.html`

**🎉 That's it! Your app should be running with all features working.**

---

## 🔧 **Detailed Setup Instructions**

### **Prerequisites in BAS:**
- ✅ **Dev Space**: "Full Stack Cloud Application" or "SAP Cloud Application Programming Model"
- ✅ **Extensions**: CDS Tools, UI5 Tools (pre-installed)
- ✅ **Node.js**: Pre-installed in BAS
- ✅ **Python**: For AI service (optional)

### **Step-by-Step Setup:**

#### **1. Project Setup**
```bash
# Verify CDS installation
cds version

# Install project dependencies
npm install

# Install global CDS tools (if needed)
npm install -g @sap/cds-dk
```

#### **2. Database Setup**
```bash
# Deploy SQLite database with sample data
cds deploy --to sqlite

# Verify data is loaded
ls -la *.sqlite
```

#### **3. Start Backend Service**
```bash
# Option A: CDS Watch (Recommended)
cds watch --port 4004

# Option B: Using npm script
npm run bas:serve

# Option C: Development mode
npm run bas:dev
```

#### **4. Start AI Service (Optional)**
```bash
# Open new terminal tab in BAS
cd flask-api

# Install Python dependencies
pip3 install -r requirements.txt

# Start AI service
python3 app.py
```

#### **5. Access Application**

**Method 1: BAS Auto-Detection**
- BAS detects port 4004 automatically
- Click "Expose and Open" in the popup
- Add `/reconciliation-dashboard/webapp/index.html` to URL

**Method 2: Manual Preview**
1. Right-click `app/reconciliation-dashboard/webapp/index.html`
2. Select "Preview Application"
3. Choose "Run as Web Application"

**Method 3: Port Preview**
1. Go to "View" → "Find Command"
2. Type "Ports: Preview"
3. Select port 4004
4. Navigate to the app path

---

## 🎯 **Verification Checklist**

### **✅ Backend Working:**
```bash
# Test CAP service endpoints
curl http://localhost:4004/reconciliation/Plants
curl http://localhost:4004/reconciliation/Products
curl http://localhost:4004/reconciliation/ReconciliationResults
```

### **✅ Frontend Working:**
- **Dashboard Tab**: Shows KPIs and reconciliation data
- **Upload Tab**: Shows plant/product dropdowns
- **Reconciliation Tab**: Shows reconciliation results
- **Approvals Tab**: Shows AI recommendations
- **Actions Tab**: Shows action tracking

### **✅ Navigation Working:**
- All tabs clickable and functional
- Back navigation works
- No console errors in browser

---

## 🛠️ **BAS-Specific Features**

### **1. Integrated Development**
- **Live Reload**: Changes reflect immediately
- **Multi-Terminal**: Run multiple services
- **Git Integration**: Built-in version control
- **Debugging**: Integrated debugger support

### **2. Port Management**
- **Auto-Detection**: BAS detects running services
- **Port Forwarding**: Automatic HTTPS endpoints
- **External Access**: Share with team members

### **3. Preview Options**
- **HTML Preview**: Right-click any HTML file
- **Application Preview**: Full app preview
- **Mobile Preview**: Test responsive design

---

## 🚨 **Troubleshooting in BAS**

### **Issue: Port Already in Use**
```bash
# Kill existing processes
pkill -f "cds"
pkill -f "node"

# Check what's using the port
lsof -i :4004

# Restart with different port
cds watch --port 4005
```

### **Issue: Database Not Found**
```bash
# Remove old database
rm -f *.sqlite

# Redeploy with fresh data
cds deploy --to sqlite
```

### **Issue: Dependencies Missing**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# For Python (AI service)
cd flask-api
pip3 install --upgrade -r requirements.txt
```

### **Issue: UI Not Loading**
```bash
# Check if service is running
curl http://localhost:4004

# Clear browser cache
# Try incognito/private mode
# Check browser console for errors
```

### **Issue: BAS Preview Not Working**
1. **Check Port**: Ensure service is running on correct port
2. **Refresh**: Try refreshing the preview
3. **Manual URL**: Use the full URL with correct path
4. **Permissions**: Check if port is properly exposed

---

## 🎨 **Development Workflow in BAS**

### **For Backend Changes:**
```bash
# CDS watch automatically reloads
# Just save your .cds or .js files
# Refresh browser to see changes
```

### **For Frontend Changes:**
```bash
# UI5 changes are immediate
# Save .xml, .js, or .json files
# Refresh browser to see changes
```

### **For Database Schema Changes:**
```bash
# Modify schema.cds
# Redeploy database
cds deploy --to sqlite

# Or use migrations for production
```

---

## 🚀 **Production Deployment from BAS**

### **Build for Production:**
```bash
# Build the project
npm run bas:build

# Create MTA archive
mbt build
```

### **Deploy to SAP BTP:**
```bash
# Login to Cloud Foundry
cf login

# Deploy application
cf push reconciliation-app

# Or deploy MTA
cf deploy mta_archives/reconciliation-system_1.0.0.mtar
```

### **Deploy to HANA Cloud:**
```bash
# For HANA deployment
npm run bas:deploy
```

---

## 📊 **Success Indicators**

### **✅ Everything Working:**
- **Terminal**: Shows "Server started at http://localhost:4004"
- **BAS Popup**: Port 4004 detected and exposed
- **Browser**: Application loads without errors
- **Dashboard**: Shows KPIs with mock data
- **Navigation**: All tabs work smoothly
- **Data**: Tables show sample reconciliation data
- **AI Features**: Approvals tab shows recommendations

### **🎯 Expected URLs:**
```
# Main application
https://port4004-workspaces-ws-xxxxx.us10.trial.applicationstudio.cloud.sap/reconciliation-dashboard/webapp/index.html

# API endpoints
https://port4004-workspaces-ws-xxxxx.us10.trial.applicationstudio.cloud.sap/reconciliation/Plants
https://port4004-workspaces-ws-xxxxx.us10.trial.applicationstudio.cloud.sap/reconciliation/ReconciliationResults
```

---

## 🎉 **You're All Set!**

Your complete Demand-Supply Reconciliation System with GenAI features is now running in SAP BAS! 

**Next Steps:**
1. **Explore**: Navigate through all tabs
2. **Customize**: Modify views and controllers
3. **Extend**: Add new features
4. **Deploy**: Push to SAP BTP for production

namespace demand.supply.reconciliation;

using { cuid, managed, Currency } from '@sap/cds/common';

// Master Data Entities
entity Plants : cuid, managed {
  plantCode     : String(10) @title: 'Plant Code';
  plantName     : String(100) @title: 'Plant Name';
  location      : String(100) @title: 'Location';
  capacity      : Decimal(15,2) @title: 'Production Capacity';
  isActive      : Boolean default true @title: 'Active Status';
}

entity Products : cuid, managed {
  productCode   : String(20) @title: 'Product Code';
  productName   : String(100) @title: 'Product Name';
  category      : String(50) @title: 'Category';
  unitOfMeasure : String(10) @title: 'Unit of Measure';
  standardCost  : Decimal(15,2) @title: 'Standard Cost';
  currency      : Currency @title: 'Currency';
  isActive      : Boolean default true @title: 'Active Status';
}

entity Vendors : cuid, managed {
  vendorCode    : String(20) @title: 'Vendor Code';
  vendorName    : String(100) @title: 'Vendor Name';
  contactInfo   : String(200) @title: 'Contact Information';
  leadTime      : Integer @title: 'Lead Time (Days)';
  reliability   : Decimal(3,2) @title: 'Reliability Score (0-1)';
  isActive      : Boolean default true @title: 'Active Status';
}

// Demand and Supply Data
entity Demand : cuid, managed {
  demandDate    : Date @title: 'Demand Date';
  plant         : Association to Plants @title: 'Plant';
  product       : Association to Products @title: 'Product';
  quantity      : Decimal(15,2) @title: 'Demand Quantity';
  priority      : String(10) @title: 'Priority' @assert.range enum { HIGH; MEDIUM; LOW };
  customerOrder : String(20) @title: 'Customer Order';
  dueDate       : Date @title: 'Due Date';
  status        : String(20) @title: 'Status' @assert.range enum { OPEN; PLANNED; FULFILLED; CANCELLED };
}

entity Supply : cuid, managed {
  supplyDate    : Date @title: 'Supply Date';
  plant         : Association to Plants @title: 'Plant';
  product       : Association to Products @title: 'Product';
  quantity      : Decimal(15,2) @title: 'Supply Quantity';
  sourceType    : String(20) @title: 'Source Type' @assert.range enum { PRODUCTION; PROCUREMENT; TRANSFER };
  sourceRef     : String(50) @title: 'Source Reference';
  availableDate : Date @title: 'Available Date';
  status        : String(20) @title: 'Status' @assert.range enum { PLANNED; CONFIRMED; RECEIVED; CONSUMED };
}

entity Stock : cuid, managed {
  plant         : Association to Plants @title: 'Plant';
  product       : Association to Products @title: 'Product';
  quantity      : Decimal(15,2) @title: 'Stock Quantity';
  reservedQty   : Decimal(15,2) @title: 'Reserved Quantity';
  availableQty  : Decimal(15,2) @title: 'Available Quantity';
  lastUpdated   : DateTime @title: 'Last Updated';
}

// Reconciliation Results
entity ReconciliationResults : cuid, managed {
  reconciliationDate : Date @title: 'Reconciliation Date';
  plant             : Association to Plants @title: 'Plant';
  product           : Association to Products @title: 'Product';
  demandQty         : Decimal(15,2) @title: 'Total Demand';
  supplyQty         : Decimal(15,2) @title: 'Total Supply';
  stockQty          : Decimal(15,2) @title: 'Current Stock';
  variance          : Decimal(15,2) @title: 'Variance (Supply - Demand)';
  variancePercent   : Decimal(5,2) @title: 'Variance Percentage';
  status            : String(20) @title: 'Status' @assert.range enum { SURPLUS; SHORTAGE; BALANCED };
  riskLevel         : String(10) @title: 'Risk Level' @assert.range enum { HIGH; MEDIUM; LOW };
  recommendations   : Composition of many GenAIRecommendations on recommendations.reconciliation = $self;
}

// GenAI Recommendations
entity GenAIRecommendations : cuid, managed {
  reconciliation    : Association to ReconciliationResults @title: 'Reconciliation';
  recommendationType: String(30) @title: 'Recommendation Type' @assert.range enum { STOCK_REALLOCATION; PROCUREMENT_REQUEST; PRODUCTION_ORDER };
  description       : String(500) @title: 'Description';
  priority          : String(10) @title: 'Priority' @assert.range enum { HIGH; MEDIUM; LOW };
  confidence        : Decimal(3,2) @title: 'AI Confidence Score (0-1)';
  estimatedCost     : Decimal(15,2) @title: 'Estimated Cost';
  estimatedTime     : Integer @title: 'Estimated Time (Days)';
  autoTrigger       : Boolean @title: 'Auto Trigger Eligible';
  status            : String(20) @title: 'Status' @assert.range enum { PENDING; APPROVED; REJECTED; EXECUTED };
  approvedBy        : String(100) @title: 'Approved By';
  approvedAt        : DateTime @title: 'Approved At';
  executedAt        : DateTime @title: 'Executed At';
  aiModel           : String(50) @title: 'AI Model Used';
  aiPrompt          : String(2000) @title: 'AI Prompt';
  aiResponse        : String(2000) @title: 'AI Response';
}

// Action Entities
entity StockReallocations : cuid, managed {
  recommendation    : Association to GenAIRecommendations @title: 'Recommendation';
  fromPlant         : Association to Plants @title: 'From Plant';
  toPlant           : Association to Plants @title: 'To Plant';
  product           : Association to Products @title: 'Product';
  quantity          : Decimal(15,2) @title: 'Quantity to Transfer';
  requestedDate     : Date @title: 'Requested Date';
  plannedDate       : Date @title: 'Planned Transfer Date';
  actualDate        : Date @title: 'Actual Transfer Date';
  transportCost     : Decimal(15,2) @title: 'Transport Cost';
  status            : String(20) @title: 'Status' @assert.range enum { PENDING; APPROVED; IN_TRANSIT; COMPLETED; CANCELLED };
  trackingNumber    : String(50) @title: 'Tracking Number';
}

entity ProcurementRequests : cuid, managed {
  recommendation    : Association to GenAIRecommendations @title: 'Recommendation';
  plant             : Association to Plants @title: 'Plant';
  product           : Association to Products @title: 'Product';
  vendor            : Association to Vendors @title: 'Preferred Vendor';
  quantity          : Decimal(15,2) @title: 'Quantity to Procure';
  requestedDate     : Date @title: 'Requested Date';
  requiredDate      : Date @title: 'Required Date';
  estimatedCost     : Decimal(15,2) @title: 'Estimated Cost';
  actualCost        : Decimal(15,2) @title: 'Actual Cost';
  status            : String(20) @title: 'Status' @assert.range enum { PENDING; APPROVED; ORDERED; RECEIVED; CANCELLED };
  purchaseOrder     : String(20) @title: 'Purchase Order Number';
  deliveryDate      : Date @title: 'Delivery Date';
}

entity ProductionOrders : cuid, managed {
  recommendation    : Association to GenAIRecommendations @title: 'Recommendation';
  plant             : Association to Plants @title: 'Plant';
  product           : Association to Products @title: 'Product';
  quantity          : Decimal(15,2) @title: 'Quantity to Produce';
  requestedDate     : Date @title: 'Requested Date';
  plannedStartDate  : Date @title: 'Planned Start Date';
  plannedEndDate    : Date @title: 'Planned End Date';
  actualStartDate   : Date @title: 'Actual Start Date';
  actualEndDate     : Date @title: 'Actual End Date';
  estimatedCost     : Decimal(15,2) @title: 'Estimated Cost';
  actualCost        : Decimal(15,2) @title: 'Actual Cost';
  status            : String(20) @title: 'Status' @assert.range enum { PENDING; APPROVED; SCHEDULED; IN_PROGRESS; COMPLETED; CANCELLED };
  workOrder         : String(20) @title: 'Work Order Number';
}

// Audit and Logging
entity ReconciliationLogs : cuid, managed {
  reconciliationId  : String(50) @title: 'Reconciliation ID';
  timestamp         : DateTime @title: 'Timestamp';
  action            : String(100) @title: 'Action';
  details           : String(1000) @title: 'Details';
  userId            : String(100) @title: 'User ID';
  status            : String(20) @title: 'Status';
}

entity AIInteractionLogs : cuid, managed {
  sessionId         : String(50) @title: 'Session ID';
  timestamp         : DateTime @title: 'Timestamp';
  endpoint          : String(100) @title: 'AI Endpoint';
  requestPayload    : String(5000) @title: 'Request Payload';
  responsePayload   : String(5000) @title: 'Response Payload';
  processingTime    : Integer @title: 'Processing Time (ms)';
  status            : String(20) @title: 'Status';
  errorMessage      : String(500) @title: 'Error Message';
}

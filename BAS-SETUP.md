# SAP Business Application Studio Setup Guide

This guide provides step-by-step instructions for setting up and running the Demand-Supply Reconciliation System in SAP Business Application Studio (BAS).

## 🚀 Prerequisites

- SAP BTP account with BAS access
- HANA Cloud instance provisioned
- Python extension installed in BAS (for AI service)

## 📋 Step-by-Step Setup

### 1. Open Project in BAS

1. **Create a new Dev Space**
   - Go to SAP Business Application Studio
   - Create a new dev space with "SAP Cloud Application Programming Model" template
   - Include additional extensions: Python Tools

2. **Clone or Import Project**
   ```bash
   # If cloning from repository
   git clone <your-repository-url>
   cd demand-supply-reconciliation-system
   
   # Or create new project and copy files
   ```

### 2. Install Dependencies

```bash
# Install Node.js dependencies
npm run bas:setup

# Install Python dependencies for AI service
cd flask-api
pip3 install -r requirements.txt
cd ..
```

### 3. Configure HANA Cloud Connection

#### Option A: Using default-env.json (Recommended for BAS)

Create `default-env.json` in the root directory:

```json
{
  "VCAP_SERVICES": {
    "hana": [
      {
        "name": "reconciliation-db",
        "label": "hana",
        "credentials": {
          "host": "your-hana-host.hanacloud.ondemand.com",
          "port": "443",
          "user": "your-username",
          "password": "your-password",
          "schema": "RECONCILIATION",
          "encrypt": true,
          "sslValidateCertificate": false
        }
      }
    ]
  }
}
```

#### Option B: Using .env file

Create `.env` file in the root directory:

```bash
# HANA Cloud Configuration
HANA_HOST=your-hana-host.hanacloud.ondemand.com
HANA_PORT=443
HANA_USER=your-username
HANA_PASSWORD=your-password
HANA_SCHEMA=RECONCILIATION

# AI Service Configuration
AI_SERVICE_URL=http://localhost:5000
CONFIDENCE_THRESHOLD=0.8
AUTO_TRIGGER_ENABLED=true
```

### 4. Deploy Database Schema

```bash
# Build the project
npm run build

# Deploy to HANA Cloud
npm run bas:deploy

# Alternative: Deploy using CDS CLI
cds deploy --to hana
```

### 5. Seed Sample Data

```bash
# Generate sample data
npm run seed
```

### 6. Start Development Services

#### Terminal 1: Start CAP Backend
```bash
npm run bas:dev
# or
cds watch --open
```

#### Terminal 2: Start AI Service
```bash
cd flask-api
python3 app.py
```

### 7. Access the Application

- **UI5 Dashboard**: Click the link provided by `cds watch` or go to the exposed port
- **CAP Services**: `https://your-bas-url/reconciliation/`
- **AI API**: `https://your-bas-url:5000/` (if port forwarding is enabled)

## 🔧 BAS-Specific Configuration

### 1. Port Configuration

BAS automatically handles port forwarding. The default ports are:
- CAP Service: 4004
- AI Service: 5000

### 2. Environment Variables in BAS

You can set environment variables in the BAS terminal:

```bash
export AI_SERVICE_URL=http://localhost:5000
export CONFIDENCE_THRESHOLD=0.8
export AUTO_TRIGGER_ENABLED=true
```

### 3. HANA Cloud Service Binding

If using service binding in BAS:

1. Go to BAS Service Center
2. Create HANA Cloud service instance
3. Bind to your application
4. The connection will be automatically configured

### 4. Python Environment Setup

```bash
# Check Python version
python3 --version

# Install pip if not available
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python3 get-pip.py

# Install virtual environment (optional but recommended)
python3 -m venv venv
source venv/bin/activate
pip install -r flask-api/requirements.txt
```

## 🧪 Testing in BAS

### 1. Run Integration Tests

```bash
# Test the complete setup
npm run test-integration
```

### 2. Manual Testing

```bash
# Test CAP service
curl http://localhost:4004/reconciliation/$metadata

# Test AI service
curl http://localhost:5000/health

# Test reconciliation endpoint
curl -X POST http://localhost:4004/reconciliation/runReconciliation \
  -H "Content-Type: application/json" \
  -d '{
    "plantCode": "",
    "productCode": "",
    "reconciliationDate": "2024-01-15"
  }'
```

## 🚀 Deployment from BAS

### 1. Deploy to SAP BTP Cloud Foundry

```bash
# Login to Cloud Foundry
cf login -a <api-endpoint>

# Create services
cf create-service hana hdi-shared reconciliation-db
cf create-service xsuaa application reconciliation-auth

# Build for production
npm run build

# Deploy
cf push
```

### 2. Create manifest.yml

```yaml
applications:
- name: reconciliation-system
  memory: 1G
  disk_quota: 1G
  buildpacks:
    - nodejs_buildpack
  services:
    - reconciliation-db
    - reconciliation-auth
  env:
    NODE_ENV: production
    AI_SERVICE_URL: https://your-ai-service-url
```

## 🔍 Troubleshooting BAS Issues

### 1. HANA Connection Issues

```bash
# Test HANA connectivity
cds deploy --dry-run

# Check HANA credentials
echo $VCAP_SERVICES

# Verify schema exists
# Connect to HANA and check schema
```

### 2. Python/AI Service Issues

```bash
# Check Python installation
which python3
python3 --version

# Check pip packages
pip3 list

# Test AI service manually
cd flask-api
python3 -c "import flask; print('Flask installed successfully')"
```

### 3. Port Access Issues

```bash
# Check if ports are available
netstat -tulpn | grep :4004
netstat -tulpn | grep :5000

# Kill processes if needed
pkill -f "cds watch"
pkill -f "python3 app.py"
```

### 4. Memory Issues in BAS

```bash
# Check memory usage
free -h

# Clear npm cache
npm cache clean --force

# Restart dev space if needed
```

## 📝 BAS Development Tips

### 1. Use BAS Terminal Effectively

- Open multiple terminals for different services
- Use `Ctrl+C` to stop services gracefully
- Use `screen` or `tmux` for persistent sessions

### 2. File Watching and Auto-Reload

```bash
# CDS watch with auto-reload
cds watch --open

# Python with auto-reload
cd flask-api
python3 app.py --debug
```

### 3. Debugging in BAS

- Use BAS built-in debugger for Node.js
- Add breakpoints in VS Code interface
- Use console.log for debugging CAP services
- Use Python debugger for AI service

### 4. Version Control in BAS

```bash
# Initialize git if needed
git init
git add .
git commit -m "Initial commit"

# Push to repository
git remote add origin <your-repo-url>
git push -u origin main
```

## 🔒 Security Considerations

### 1. Environment Variables

- Never commit `.env` or `default-env.json` to version control
- Use BAS environment variables for sensitive data
- Rotate credentials regularly

### 2. HANA Security

- Use dedicated HANA user with minimal privileges
- Enable SSL/TLS for HANA connections
- Regularly update HANA Cloud instance

## 📊 Monitoring in BAS

### 1. Application Logs

```bash
# View CAP logs
cds watch --verbose

# View AI service logs
cd flask-api
python3 app.py --debug
```

### 2. Performance Monitoring

```bash
# Monitor resource usage
top
htop

# Check disk usage
df -h
```

---

## 🆘 Support

For BAS-specific issues:
- Check SAP Community for BAS troubleshooting
- Use BAS built-in help and documentation
- Contact SAP Support if needed

For application-specific issues:
- Refer to the main README.md
- Check application logs
- Run integration tests

---

**Happy coding in SAP Business Application Studio! 🚀**

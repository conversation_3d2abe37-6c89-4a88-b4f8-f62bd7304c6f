# Demand-Supply Reconciliation System

A smart manufacturing application that analyzes demand vs. supply across manufacturing plants, detects mismatches, and uses GenAI to recommend reconciliation actions.

## 🎯 Features

- **Demand-Supply Analysis**: Analyze variance across manufacturing plants
- **GenAI Recommendations**: AI-powered suggestions for reconciliation actions
- **Auto-Trigger Actions**: Automatically execute high-confidence recommendations
- **Approval Workflow**: Manual approval for low-confidence suggestions
- **Action Tracking**: Monitor status of all reconciliation actions
- **Fiori Dashboard**: Modern SAP UI5 interface with KPI tiles

## 🧱 Tech Stack

- **Frontend**: SAP UI5 (Fiori-style)
- **Backend**: SAP CAP (Node.js)
- **Database**: SAP HANA Cloud
- **AI Layer**: Python Flask API with Ollama/Mistral
- **Communication**: REST API integration

## 📂 Project Structure

```
demand-supply-reconciliation-system/
├── 📁 db/                          # CDS Data Models
│   └── schema.cds                  # Entity definitions
├── 📁 srv/                         # CAP Backend Services
│   ├── service.cds                 # Service definitions
│   └── handlers.js                 # Business logic handlers
├── 📁 app/                         # UI5 Frontend
│   └── reconciliation-dashboard/
│       └── webapp/
│           ├── view/               # UI5 Views
│           ├── controller/         # UI5 Controllers
│           ├── manifest.json       # App configuration
│           └── index.html          # Entry point
├── 📁 flask-api/                   # GenAI Service
│   ├── app.py                      # Flask application
│   ├── requirements.txt            # Python dependencies
│   └── Dockerfile                  # Container configuration
├── 📁 scripts/                     # Utility scripts
│   └── seed-data.js               # Sample data generation
└── package.json                   # Node.js dependencies
```

## 🚀 Quick Start

### Prerequisites

- SAP Business Application Studio (BAS) access
- SAP HANA Cloud instance
- Python 3.11+ (available in BAS)
- Node.js 18+ and npm (pre-installed in BAS)

### 1. Setup in SAP Business Application Studio

```bash
# Install dependencies
npm run bas:setup

# Install Python dependencies for AI service
cd flask-api
pip3 install -r requirements.txt
cd ..
```

### 2. Configure HANA Cloud Connection

Create `default-env.json` in the root directory:

```json
{
  "VCAP_SERVICES": {
    "hana": [
      {
        "name": "reconciliation-db",
        "label": "hana",
        "credentials": {
          "host": "your-hana-host.hanacloud.ondemand.com",
          "port": "443",
          "user": "your-username",
          "password": "your-password",
          "schema": "RECONCILIATION",
          "encrypt": true
        }
      }
    ]
  }
}
```

### 3. Deploy Database Schema

```bash
# Build and deploy to HANA Cloud
npm run bas:deploy
```

### 4. Seed Sample Data

```bash
# Generate sample data
npm run seed
```

### 5. Start Development Services

```bash
# Terminal 1: Start CAP backend
npm run bas:dev

# Terminal 2: Start AI service
cd flask-api
python3 app.py
```

### 6. Access Application

- **UI5 Dashboard**: Available through BAS port forwarding
- **CAP Services**: `https://your-bas-url/reconciliation/`
- **AI API**: `https://your-bas-url:5000/`

📋 **For detailed BAS setup instructions, see [BAS-SETUP.md](BAS-SETUP.md)**

## 🧩 Key Modules

### 1. CDS Data Models

**Core Entities:**
- `Plants`, `Products`, `Vendors` - Master data
- `Demand`, `Supply`, `Stock` - Operational data
- `ReconciliationResults` - Analysis results
- `GenAIRecommendations` - AI suggestions
- `StockReallocations`, `ProcurementRequests`, `ProductionOrders` - Actions

### 2. CAP Backend Services

**Main Services:**
- `ReconciliationService` - Core reconciliation logic
- `AdminService` - Master data management
- `AnalyticsService` - Reporting and analytics
- `IntegrationService` - External system integration

**Key Actions:**
- `runReconciliation()` - Execute demand-supply analysis
- `approveRecommendation()` - Approve AI suggestions
- `executeAction()` - Trigger reconciliation actions

### 3. GenAI Flask API

**Endpoints:**
- `POST /recommend` - Generate reconciliation recommendations
- `GET /health` - Service health check
- `GET /models` - List available AI models

**AI Integration:**
- Ollama/Mistral for local inference
- Fallback logic when AI unavailable
- Confidence scoring for auto-trigger decisions

### 4. UI5 Frontend

**Views:**
- **Dashboard** - KPI tiles and reconciliation summary
- **Upload** - Data upload and manual entry
- **Approvals** - AI recommendation approval workflow
- **Actions** - Action tracking and execution

## 🔧 Configuration

### Environment Variables

**CAP Backend:**
```bash
AI_SERVICE_URL=http://localhost:5000
CONFIDENCE_THRESHOLD=0.8
AUTO_TRIGGER_ENABLED=true
```

**Flask AI Service:**
```bash
OLLAMA_URL=http://localhost:11434
MODEL_NAME=mistral:7b
USE_OLLAMA=true
PORT=5000
DEBUG=false
```

### AI Model Configuration

The system supports multiple AI backends:

1. **Ollama (Recommended for local development)**
   ```bash
   ollama pull mistral:7b
   export USE_OLLAMA=true
   export MODEL_NAME=mistral:7b
   ```

2. **Hugging Face (Cloud deployment)**
   ```bash
   export USE_OLLAMA=false
   export HF_MODEL=mistralai/Mistral-7B-Instruct-v0.1
   export HF_TOKEN=your-huggingface-token
   ```

## 📊 Usage Workflow

### 1. Data Upload
- Upload demand, supply, and stock data via UI or API
- Support for CSV/Excel bulk upload
- Manual data entry for individual records

### 2. Run Reconciliation
- Execute demand-supply analysis for selected plants/products
- System calculates variance and identifies mismatches
- Generates reconciliation results with risk levels

### 3. AI Recommendations
- System calls GenAI API with reconciliation data
- AI analyzes situation and suggests actions:
  - **Stock Reallocation** - Move surplus to deficit plants
  - **Procurement Request** - Purchase from external vendors
  - **Production Order** - Manufacture internally

### 4. Action Approval
- High confidence (≥80%): Auto-triggered
- Medium confidence (50-79%): Requires approval
- Low confidence (<50%): Manual review required

### 5. Action Execution
- Approved actions are executed automatically
- Status tracking: Pending → Approved → Executed
- Integration with external systems for actual execution

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
# Test AI service
curl -X POST http://localhost:5000/recommend \
  -H "Content-Type: application/json" \
  -d '{
    "plant": {"code": "P001", "name": "Plant North", "capacity": 1000},
    "product": {"code": "PROD001", "name": "Engine Component"},
    "demand": 500,
    "supply": 300,
    "stock": 100,
    "variance": -100
  }'

# Test CAP service
curl -X POST http://localhost:4004/reconciliation/runReconciliation \
  -H "Content-Type: application/json" \
  -d '{
    "plantCode": "P001",
    "productCode": "PROD001",
    "reconciliationDate": "2024-01-15"
  }'
```

## 🚢 Deployment

### Docker Deployment

```bash
# Build and run AI service
cd flask-api
docker build -t reconciliation-ai .
docker run -p 5000:5000 reconciliation-ai

# Deploy CAP to SAP BTP
cf push reconciliation-app
```

### SAP BTP Deployment

1. Configure HANA Cloud service binding
2. Deploy CAP application to Cloud Foundry
3. Configure AI service as microservice
4. Set up approuter for UI5 app

## 🔍 Monitoring

### Health Checks
- CAP: `GET /reconciliation/$metadata`
- AI Service: `GET /health`
- Database: Connection status in logs

### Logging
- Reconciliation logs in `ReconciliationLogs` entity
- AI interaction logs in `AIInteractionLogs` entity
- Application logs via CAP logging framework

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in `/docs` folder

---

**Built with ❤️ using SAP CAP, UI5, and GenAI technologies**

{"name": "demand-supply-reconciliation-system", "version": "1.0.0", "description": "Smart Manufacturing Demand-Supply Reconciliation System with GenAI", "main": "index.js", "scripts": {"start": "cds-serve", "watch": "cds watch", "build": "cds build", "deploy": "cds deploy", "test": "jest", "seed": "node scripts/seed-data.js", "test-integration": "node scripts/test-integration.js", "ai:start": "cd flask-api && python app.py", "setup": "npm install && cd flask-api && pip install -r requirements.txt", "dev": "concurrently \"npm run watch\" \"npm run ai:start\"", "lint": "eslint .", "format": "prettier --write .", "bas:setup": "npm install && cds deploy --to sqlite", "bas:deploy": "cds build && cds deploy --to hana", "bas:dev": "cds watch --port 4004", "bas:serve": "cds serve --port 4004", "bas:build": "cds build --production", "bas:start": "./start-bas.sh", "bas:reset": "rm -f *.sqlite && cds deploy --to sqlite", "bas:watch": "cds watch --port 4004 --livereload", "watch-reconciliation": "cds watch --open reconciliation/webapp/index.html?sap-ui-xx-viewCache=false"}, "keywords": ["SAP", "CAP", "GenAI", "Manufacturing", "Demand-Supply"], "author": "SAP Developer", "license": "ISC", "dependencies": {"@cap-js/hana": "^2.1.2", "@cap-js/sqlite": "^2.0.2", "@sap/cds": "^9.1.0", "@sap/cds-dk": "^9.1.2", "axios": "^1.11.0", "express": "^4.21.2", "sqlite3": "^5.1.7"}, "cds": {"requires": {"db": {"kind": "sqlite", "credentials": {"url": "db.sqlite"}}, "auth": {"kind": "dummy"}}, "hana": {"deploy-format": "hdbtable"}, "build": {"target": ".", "tasks": [{"for": "hana", "dest": "../db"}, {"for": "node-cf", "dest": "../"}]}}, "devDependencies": {"concurrently": "^9.2.0", "eslint": "^9.32.0", "jest": "^30.0.5", "prettier": "^3.6.2"}}